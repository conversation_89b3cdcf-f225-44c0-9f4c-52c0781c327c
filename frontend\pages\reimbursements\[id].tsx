import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import ConfirmationModal from '../../components/ui/ConfirmationModal';
import { api } from '../../lib/api';
import { formatCurrency, formatDate, formatRelativeTime } from '../../lib/utils';
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  TagIcon,
  PhotoIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  BanknotesIcon,
  EyeIcon,
  ArrowDownTrayIcon,
} from '@heroicons/react/24/outline';
import {
  CheckCircleIcon as CheckCircleIconSolid,
  ClockIcon as ClockIconSolid,
  XCircleIcon as XCircleIconSolid,
  BanknotesIcon as BanknotesIconSolid,
} from '@heroicons/react/24/solid';

// Form validation schema
const reimbursementSchema = z.object({
  business_id: z.string().min(1, 'Business is required'),
  description: z.string().min(1, 'Description is required').max(500, 'Description must be less than 500 characters'),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  currency: z.string().min(1, 'Currency is required'),
  expense_date: z.string().min(1, 'Expense date is required'),
  category: z.string().optional(),
  notes: z.string().optional(),
  receipt_url: z.string().optional(),
});

type ReimbursementFormData = z.infer<typeof reimbursementSchema>;

// Reimbursement status configurations
const statusConfig = {
  pending: {
    label: 'Pending',
    icon: ClockIconSolid,
    className: 'text-warning-600 bg-warning-100 dark:bg-warning-900',
    description: 'Awaiting approval',
  },
  approved: {
    label: 'Approved',
    icon: CheckCircleIconSolid,
    className: 'text-success-600 bg-success-100 dark:bg-success-900',
    description: 'Approved for payment',
  },
  rejected: {
    label: 'Rejected',
    icon: XCircleIconSolid,
    className: 'text-error-600 bg-error-100 dark:bg-error-900',
    description: 'Request rejected',
  },
  paid: {
    label: 'Paid',
    icon: BanknotesIconSolid,
    className: 'text-primary-600 bg-primary-100 dark:bg-primary-900',
    description: 'Payment completed',
  },
};

// Common expense categories
const expenseCategories = [
  'Software & Tools',
  'Marketing & Advertising',
  'Office Supplies',
  'Travel & Transportation',
  'Meals & Entertainment',
  'Professional Services',
  'Training & Education',
  'Equipment & Hardware',
  'Utilities',
  'Other',
];

// Common currencies
const currencies = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
];

export default function ReimbursementDetailPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { id } = router.query;
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showActionModal, setShowActionModal] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject' | 'mark_paid' | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isSubmitting, isDirty },
  } = useForm<ReimbursementFormData>({
    resolver: zodResolver(reimbursementSchema),
  });

  // Fetch reimbursement details
  const { data: reimbursement, isLoading } = useQuery({
    queryKey: ['reimbursement', id],
    queryFn: () => api.reimbursements.getById(id as string),
    enabled: !!id,
  });

  // Fetch businesses for form
  const { data: businesses = [] } = useQuery({
    queryKey: ['businesses'],
    queryFn: () => api.businesses.getAll(),
  });

  // Update form when reimbursement data loads
  useEffect(() => {
    if (reimbursement) {
      reset({
        business_id: reimbursement.business_id,
        description: reimbursement.description,
        amount: reimbursement.amount,
        currency: reimbursement.currency,
        expense_date: reimbursement.expense_date?.split('T')[0] || '',
        category: reimbursement.category || '',
        notes: reimbursement.notes || '',
        receipt_url: reimbursement.receipt_url || '',
      });
    }
  }, [reimbursement, reset]);

  // Update reimbursement mutation
  const updateMutation = useMutation({
    mutationFn: (data: ReimbursementFormData) => api.reimbursements.update(id as string, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reimbursement', id] });
      queryClient.invalidateQueries({ queryKey: ['reimbursements'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Reimbursement updated successfully!');
      setIsEditing(false);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update reimbursement');
    },
  });

  // Delete reimbursement mutation
  const deleteMutation = useMutation({
    mutationFn: () => api.reimbursements.delete(id as string),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reimbursements'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Reimbursement deleted successfully!');
      router.push('/reimbursements');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete reimbursement');
    },
  });

  // Approve reimbursement mutation
  const approveMutation = useMutation({
    mutationFn: () => api.reimbursements.approve(id as string),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reimbursement', id] });
      queryClient.invalidateQueries({ queryKey: ['reimbursements'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Reimbursement approved successfully!');
      setShowActionModal(false);
      setActionType(null);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to approve reimbursement');
    },
  });

  // Reject reimbursement mutation
  const rejectMutation = useMutation({
    mutationFn: () => api.reimbursements.reject(id as string),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reimbursement', id] });
      queryClient.invalidateQueries({ queryKey: ['reimbursements'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Reimbursement rejected successfully!');
      setShowActionModal(false);
      setActionType(null);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to reject reimbursement');
    },
  });

  // Mark as paid mutation
  const markPaidMutation = useMutation({
    mutationFn: () => api.reimbursements.markPaid(id as string),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reimbursement', id] });
      queryClient.invalidateQueries({ queryKey: ['reimbursements'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Reimbursement marked as paid successfully!');
      setShowActionModal(false);
      setActionType(null);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to mark reimbursement as paid');
    },
  });

  // File upload mutation
  const uploadMutation = useMutation({
    mutationFn: (file: File) => api.files.upload(file),
    onSuccess: (data) => {
      setValue('receipt_url', data.url);
      toast.success('Receipt uploaded successfully!');
      setIsUploading(false);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to upload receipt');
      setIsUploading(false);
    },
  });

  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please upload a valid image (JPEG, PNG, GIF) or PDF file');
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }

    setUploadedFile(file);
    setIsUploading(true);

    // Create preview for images
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => setPreviewUrl(e.target?.result as string);
      reader.readAsDataURL(file);
    } else {
      setPreviewUrl(null);
    }

    // Upload file
    uploadMutation.mutate(file);
  };

  // Remove uploaded file
  const removeFile = () => {
    setUploadedFile(null);
    setPreviewUrl(null);
    setValue('receipt_url', '');
  };

  const handleAction = (action: 'approve' | 'reject' | 'mark_paid') => {
    setActionType(action);
    setShowActionModal(true);
  };

  const confirmAction = () => {
    if (!actionType) return;
    
    switch (actionType) {
      case 'approve':
        approveMutation.mutate();
        break;
      case 'reject':
        rejectMutation.mutate();
        break;
      case 'mark_paid':
        markPaidMutation.mutate();
        break;
    }
  };

  const onSubmit = (data: ReimbursementFormData) => {
    updateMutation.mutate(data);
  };

  const getBusinessName = (businessId: string) => {
    const business = businesses.find((b: any) => b.id === businessId);
    return business?.name || 'Unknown Business';
  };

  const getActionModalContent = () => {
    switch (actionType) {
      case 'approve':
        return {
          title: 'Approve Reimbursement',
          message: 'Are you sure you want to approve this reimbursement request?',
          confirmText: 'Approve',
          confirmVariant: 'primary' as const,
        };
      case 'reject':
        return {
          title: 'Reject Reimbursement',
          message: 'Are you sure you want to reject this reimbursement request? This action cannot be undone.',
          confirmText: 'Reject',
          confirmVariant: 'danger' as const,
        };
      case 'mark_paid':
        return {
          title: 'Mark as Paid',
          message: 'Are you sure you want to mark this reimbursement as paid?',
          confirmText: 'Mark as Paid',
          confirmVariant: 'primary' as const,
        };
      default:
        return {
          title: '',
          message: '',
          confirmText: '',
          confirmVariant: 'primary' as const,
        };
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading reimbursement..." />
        </div>
      </Layout>
    );
  }

  if (!reimbursement) {
    return (
      <Layout>
        <div className="text-center py-12">
          <DocumentTextIcon className="h-12 w-12 text-text-tertiary mx-auto mb-4" />
          <h3 className="text-lg font-medium text-text-primary mb-2">Reimbursement Not Found</h3>
          <p className="text-text-secondary mb-6">
            The reimbursement you're looking for doesn't exist or has been deleted.
          </p>
          <Link href="/reimbursements" className="btn-primary">
            Back to Reimbursements
          </Link>
        </div>
      </Layout>
    );
  }

  const status = reimbursement.status as keyof typeof statusConfig;
  const StatusIcon = statusConfig[status]?.icon || ClockIconSolid;
  const actionModalContent = getActionModalContent();

  return (
    <>
      <Head>
        <title>{reimbursement.description} - SubDash</title>
        <meta name="description" content={`Reimbursement details for ${reimbursement.description}`} />
      </Head>

      <Layout>
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                href="/reimbursements"
                className="p-2 hover:bg-background-secondary rounded-lg transition-colors"
              >
                <ArrowLeftIcon className="h-5 w-5 text-text-secondary" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-text-primary">
                  {reimbursement.description}
                </h1>
                <p className="text-sm text-text-secondary mt-1">
                  Submitted {formatRelativeTime(reimbursement.created_at)}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              {/* Status Actions */}
              {reimbursement.status === 'pending' && (
                <>
                  <button
                    onClick={() => handleAction('approve')}
                    className="btn-primary flex items-center"
                  >
                    <CheckCircleIcon className="h-4 w-4 mr-2" />
                    Approve
                  </button>
                  <button
                    onClick={() => handleAction('reject')}
                    className="btn-danger flex items-center"
                  >
                    <XCircleIcon className="h-4 w-4 mr-2" />
                    Reject
                  </button>
                </>
              )}
              
              {reimbursement.status === 'approved' && (
                <button
                  onClick={() => handleAction('mark_paid')}
                  className="btn-primary flex items-center"
                >
                  <BanknotesIcon className="h-4 w-4 mr-2" />
                  Mark as Paid
                </button>
              )}
              
              {/* Edit/Delete Actions */}
              <button
                onClick={() => setIsEditing(!isEditing)}
                className={`btn-secondary flex items-center ${
                  isEditing ? 'bg-primary-100 text-primary-700 dark:bg-primary-900' : ''
                }`}
              >
                <PencilIcon className="h-4 w-4 mr-2" />
                {isEditing ? 'Cancel Edit' : 'Edit'}
              </button>
              
              <button
                onClick={() => setShowDeleteModal(true)}
                className="btn-danger flex items-center"
              >
                <TrashIcon className="h-4 w-4 mr-2" />
                Delete
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {isEditing ? (
                /* Edit Form */
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {/* Basic Information */}
                  <div className="card">
                    <h2 className="text-lg font-semibold text-text-primary mb-4 flex items-center">
                      <DocumentTextIcon className="h-5 w-5 mr-2" />
                      Basic Information
                    </h2>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Business *
                        </label>
                        <select
                          {...register('business_id')}
                          className={`input-primary w-full ${
                            errors.business_id ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        >
                          <option value="">Select a business</option>
                          {businesses.map((business: any) => (
                            <option key={business.id} value={business.id}>
                              {business.name}
                            </option>
                          ))}
                        </select>
                        {errors.business_id && (
                          <p className="text-error-600 text-sm mt-1">{errors.business_id.message}</p>
                        )}
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Description *
                        </label>
                        <textarea
                          {...register('description')}
                          rows={3}
                          className={`input-primary w-full ${
                            errors.description ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        />
                        {errors.description && (
                          <p className="text-error-600 text-sm mt-1">{errors.description.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Amount *
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          min="0"
                          {...register('amount', { valueAsNumber: true })}
                          className={`input-primary w-full ${
                            errors.amount ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        />
                        {errors.amount && (
                          <p className="text-error-600 text-sm mt-1">{errors.amount.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Currency *
                        </label>
                        <select
                          {...register('currency')}
                          className={`input-primary w-full ${
                            errors.currency ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        >
                          {currencies.map((currency) => (
                            <option key={currency.code} value={currency.code}>
                              {currency.code} - {currency.name}
                            </option>
                          ))}
                        </select>
                        {errors.currency && (
                          <p className="text-error-600 text-sm mt-1">{errors.currency.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Expense Date *
                        </label>
                        <input
                          type="date"
                          {...register('expense_date')}
                          className={`input-primary w-full ${
                            errors.expense_date ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        />
                        {errors.expense_date && (
                          <p className="text-error-600 text-sm mt-1">{errors.expense_date.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Category
                        </label>
                        <select
                          {...register('category')}
                          className="input-primary w-full"
                        >
                          {expenseCategories.map((category) => (
                            <option key={category} value={category}>
                              {category}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Receipt Upload */}
                  <div className="card">
                    <h2 className="text-lg font-semibold text-text-primary mb-4 flex items-center">
                      <PhotoIcon className="h-5 w-5 mr-2" />
                      Receipt Upload
                    </h2>
                    
                    <div className="space-y-4">
                      <div className="border-2 border-dashed border-border-secondary rounded-lg p-6 text-center">
                        {uploadedFile || watch('receipt_url') ? (
                          <div className="space-y-4">
                            {(previewUrl || watch('receipt_url')) && (
                              <div className="max-w-xs mx-auto">
                                <img
                                  src={previewUrl || watch('receipt_url')}
                                  alt="Receipt preview"
                                  className="w-full h-auto rounded-lg shadow-sm"
                                />
                              </div>
                            )}
                            <div>
                              <p className="text-sm font-medium text-text-primary">
                                {uploadedFile?.name || 'Receipt attached'}
                              </p>
                              {uploadedFile && (
                                <p className="text-xs text-text-secondary">
                                  {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                                </p>
                              )}
                            </div>
                            <button
                              type="button"
                              onClick={removeFile}
                              className="text-error-600 hover:text-error-700 text-sm font-medium"
                            >
                              Remove
                            </button>
                          </div>
                        ) : (
                          <div>
                            <PhotoIcon className="h-8 w-8 text-text-tertiary mx-auto mb-2" />
                            <p className="text-sm text-text-primary mb-1">Upload receipt (optional)</p>
                            <p className="text-xs text-text-secondary mb-4">
                              Supports JPEG, PNG, GIF, PDF up to 5MB
                            </p>
                            <label className="btn-secondary cursor-pointer">
                              Choose File
                              <input
                                type="file"
                                accept="image/*,.pdf"
                                onChange={handleFileUpload}
                                className="hidden"
                                disabled={isUploading}
                              />
                            </label>
                          </div>
                        )}
                        
                        {isUploading && (
                          <div className="mt-4">
                            <LoadingSpinner size="sm" text="Uploading..." />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Additional Information */}
                  <div className="card">
                    <h2 className="text-lg font-semibold text-text-primary mb-4 flex items-center">
                      <InformationCircleIcon className="h-5 w-5 mr-2" />
                      Additional Information
                    </h2>
                    
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        Notes
                      </label>
                      <textarea
                        {...register('notes')}
                        rows={3}
                        placeholder="Any additional notes or context for this expense..."
                        className="input-primary w-full"
                      />
                    </div>
                  </div>

                  {/* Form Actions */}
                  <div className="flex flex-col sm:flex-row gap-3 sm:justify-end">
                    <button
                      type="button"
                      onClick={() => setIsEditing(false)}
                      className="btn-secondary"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting || updateMutation.isPending || !isDirty}
                      className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting || updateMutation.isPending ? (
                        <>
                          <LoadingSpinner size="sm" className="mr-2" />
                          Updating...
                        </>
                      ) : (
                        'Save Changes'
                      )}
                    </button>
                  </div>
                </form>
              ) : (
                /* View Mode */
                <div className="space-y-6">
                  {/* Basic Information */}
                  <div className="card">
                    <h2 className="text-lg font-semibold text-text-primary mb-4 flex items-center">
                      <DocumentTextIcon className="h-5 w-5 mr-2" />
                      Reimbursement Details
                    </h2>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-text-secondary mb-1">
                          Business
                        </label>
                        <div className="flex items-center">
                          <BuildingOfficeIcon className="h-4 w-4 text-text-tertiary mr-2" />
                          <span className="text-text-primary">
                            {getBusinessName(reimbursement.business_id)}
                          </span>
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-text-secondary mb-1">
                          Amount
                        </label>
                        <div className="flex items-center">
                          <CurrencyDollarIcon className="h-4 w-4 text-text-tertiary mr-2" />
                          <span className="text-lg font-semibold text-text-primary">
                            {formatCurrency(reimbursement.amount, reimbursement.currency)}
                          </span>
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-text-secondary mb-1">
                          Expense Date
                        </label>
                        <div className="flex items-center">
                          <CalendarIcon className="h-4 w-4 text-text-tertiary mr-2" />
                          <span className="text-text-primary">
                            {formatDate(reimbursement.expense_date)}
                          </span>
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-text-secondary mb-1">
                          Category
                        </label>
                        <div className="flex items-center">
                          <TagIcon className="h-4 w-4 text-text-tertiary mr-2" />
                          <span className="text-text-primary capitalize">
                            {reimbursement.category || 'Other'}
                          </span>
                        </div>
                      </div>
                      
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-text-secondary mb-1">
                          Description
                        </label>
                        <p className="text-text-primary">{reimbursement.description}</p>
                      </div>
                      
                      {reimbursement.notes && (
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-text-secondary mb-1">
                            Notes
                          </label>
                          <p className="text-text-primary">{reimbursement.notes}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Receipt */}
                  {reimbursement.receipt_url && (
                    <div className="card">
                      <h2 className="text-lg font-semibold text-text-primary mb-4 flex items-center">
                        <PhotoIcon className="h-5 w-5 mr-2" />
                        Receipt
                      </h2>
                      
                      <div className="space-y-4">
                        <div className="max-w-md">
                          <img
                            src={reimbursement.receipt_url}
                            alt="Receipt"
                            className="w-full h-auto rounded-lg shadow-sm border border-border-primary"
                          />
                        </div>
                        <a
                          href={reimbursement.receipt_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="btn-secondary inline-flex items-center"
                        >
                          <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                          Download Receipt
                        </a>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Status Card */}
              <div className="card">
                <h3 className="text-lg font-semibold text-text-primary mb-4">Status</h3>
                <div className="space-y-4">
                  <div className={`inline-flex items-center px-3 py-2 rounded-full text-sm font-medium ${
                    statusConfig[status]?.className || 'text-text-secondary bg-background-secondary'
                  }`}>
                    <StatusIcon className="h-4 w-4 mr-2" />
                    {statusConfig[status]?.label || reimbursement.status}
                  </div>
                  
                  <p className="text-sm text-text-secondary">
                    {statusConfig[status]?.description || 'Status information'}
                  </p>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-text-secondary">Submitted:</span>
                      <span className="text-text-primary">
                        {formatDate(reimbursement.created_at)}
                      </span>
                    </div>
                    
                    {reimbursement.updated_at !== reimbursement.created_at && (
                      <div className="flex justify-between">
                        <span className="text-text-secondary">Updated:</span>
                        <span className="text-text-primary">
                          {formatDate(reimbursement.updated_at)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="card">
                <h3 className="text-lg font-semibold text-text-primary mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <Link
                    href="/reimbursements"
                    className="btn-secondary w-full flex items-center justify-center"
                  >
                    <EyeIcon className="h-4 w-4 mr-2" />
                    View All Reimbursements
                  </Link>
                  
                  <Link
                    href="/reimbursements/new"
                    className="btn-secondary w-full flex items-center justify-center"
                  >
                    <DocumentTextIcon className="h-4 w-4 mr-2" />
                    Submit New Request
                  </Link>
                  
                  <Link
                    href={`/businesses/${reimbursement.business_id}`}
                    className="btn-secondary w-full flex items-center justify-center"
                  >
                    <BuildingOfficeIcon className="h-4 w-4 mr-2" />
                    View Business
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        <ConfirmationModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={() => deleteMutation.mutate()}
          title="Delete Reimbursement"
          message="Are you sure you want to delete this reimbursement? This action cannot be undone."
          confirmText="Delete"
          confirmVariant="danger"
          isLoading={deleteMutation.isPending}
        />

        {/* Action Confirmation Modal */}
        <ConfirmationModal
          isOpen={showActionModal}
          onClose={() => {
            setShowActionModal(false);
            setActionType(null);
          }}
          onConfirm={confirmAction}
          title={actionModalContent.title}
          message={actionModalContent.message}
          confirmText={actionModalContent.confirmText}
          confirmVariant={actionModalContent.confirmVariant}
          isLoading={
            approveMutation.isPending || 
            rejectMutation.isPending || 
            markPaidMutation.isPending
          }
        />
      </Layout>
    </>
  );
}