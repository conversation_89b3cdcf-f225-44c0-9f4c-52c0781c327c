import { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { api } from '../../lib/api';
import {
  ArrowLeftIcon,
  BuildingOfficeIcon,
  GlobeAltIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
} from '@heroicons/react/24/outline';

// Validation schema
const businessSchema = z.object({
  name: z.string().min(2, 'Business name must be at least 2 characters'),
  description: z.string().optional(),
  industry: z.string().optional(),
  website: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  phone: z.string().optional(),
  email: z.string().email('Please enter a valid email').optional().or(z.literal('')),
  address: z.string().optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  currency: z.string().min(3, 'Please select a currency'),
  timezone: z.string().min(1, 'Please select a timezone'),
});

type BusinessFormData = z.infer<typeof businessSchema>;

// Common currencies
const currencies = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'KWD', name: 'Kuwaiti Dinar', symbol: 'د.ك' },
  { code: 'SAR', name: 'Saudi Riyal', symbol: 'ر.س' },
  { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ' },
  { code: 'QAR', name: 'Qatari Riyal', symbol: 'ر.ق' },
  { code: 'BHD', name: 'Bahraini Dinar', symbol: '.د.ب' },
  { code: 'OMR', name: 'Omani Rial', symbol: 'ر.ع.' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
];

// Common timezones
const timezones = [
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },
  { value: 'Europe/Paris', label: 'Central European Time (CET)' },
  { value: 'Asia/Kuwait', label: 'Kuwait Time (AST)' },
  { value: 'Asia/Riyadh', label: 'Arabia Standard Time (AST)' },
  { value: 'Asia/Dubai', label: 'Gulf Standard Time (GST)' },
  { value: 'Asia/Qatar', label: 'Arabia Standard Time (AST)' },
  { value: 'Asia/Bahrain', label: 'Arabia Standard Time (AST)' },
  { value: 'Asia/Muscat', label: 'Gulf Standard Time (GST)' },
  { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },
  { value: 'Asia/Shanghai', label: 'China Standard Time (CST)' },
  { value: 'Asia/Kolkata', label: 'India Standard Time (IST)' },
  { value: 'Australia/Sydney', label: 'Australian Eastern Time (AET)' },
];

// Common industries
const industries = [
  'Technology',
  'Healthcare',
  'Finance',
  'Education',
  'Retail',
  'Manufacturing',
  'Construction',
  'Real Estate',
  'Transportation',
  'Energy',
  'Media & Entertainment',
  'Food & Beverage',
  'Consulting',
  'Legal',
  'Non-profit',
  'Government',
  'Other',
];

export default function NewBusinessPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<BusinessFormData>({
    resolver: zodResolver(businessSchema),
    defaultValues: {
      name: '',
      description: '',
      industry: '',
      website: '',
      phone: '',
      email: '',
      address: '',
      city: '',
      country: '',
      currency: 'USD',
      timezone: 'America/New_York',
    },
  });

  // Create business mutation
  const createMutation = useMutation({
    mutationFn: (data: BusinessFormData) => api.businesses.create(data),
    onSuccess: (business: any) => {
      queryClient.invalidateQueries({ queryKey: ['businesses'] });
      toast.success('Business created successfully!');
      router.push(`/businesses/${business.id}`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create business');
      setIsSubmitting(false);
    },
  });

  const onSubmit = async (data: BusinessFormData) => {
    setIsSubmitting(true);
    
    // Clean up empty strings
    const cleanedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === 'string' && value.trim() === '' ? undefined : value,
      ])
    ) as BusinessFormData;

    createMutation.mutate(cleanedData);
  };

  return (
    <>
      <Head>
        <title>New Business - SubDash</title>
        <meta name="description" content="Create a new business" />
      </Head>

      <Layout>
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <Link
                href="/businesses"
                className="mr-4 p-2 rounded-md text-text-secondary hover:text-text-primary hover:bg-background-secondary transition-colors"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-text-primary">Create New Business</h1>
                <p className="text-sm text-text-secondary mt-1">
                  Add a new business to manage its subscriptions and expenses
                </p>
              </div>
            </div>
          </div>

          {/* Form */}
          <div className="card">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Basic Information */}
              <div>
                <h2 className="text-lg font-medium text-text-primary mb-4 flex items-center">
                  <BuildingOfficeIcon className="h-5 w-5 mr-2" />
                  Basic Information
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Business Name */}
                  <div className="md:col-span-2">
                    <label htmlFor="name" className="block text-sm font-medium text-text-primary mb-1">
                      Business Name *
                    </label>
                    <input
                      {...register('name')}
                      type="text"
                      className={`input-primary w-full ${
                        errors.name ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                      }`}
                      placeholder="Enter business name"
                    />
                    {errors.name && (
                      <p className="mt-1 text-sm text-error-600">{errors.name.message}</p>
                    )}
                  </div>

                  {/* Industry */}
                  <div>
                    <label htmlFor="industry" className="block text-sm font-medium text-text-primary mb-1">
                      Industry
                    </label>
                    <select
                      {...register('industry')}
                      className="input-primary w-full"
                    >
                      <option value="">Select industry</option>
                      {industries.map((industry) => (
                        <option key={industry} value={industry}>
                          {industry}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Currency */}
                  <div>
                    <label htmlFor="currency" className="block text-sm font-medium text-text-primary mb-1">
                      Currency *
                    </label>
                    <select
                      {...register('currency')}
                      className={`input-primary w-full ${
                        errors.currency ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                      }`}
                    >
                      {currencies.map((currency) => (
                        <option key={currency.code} value={currency.code}>
                          {currency.code} - {currency.name} ({currency.symbol})
                        </option>
                      ))}
                    </select>
                    {errors.currency && (
                      <p className="mt-1 text-sm text-error-600">{errors.currency.message}</p>
                    )}
                  </div>

                  {/* Timezone */}
                  <div className="md:col-span-2">
                    <label htmlFor="timezone" className="block text-sm font-medium text-text-primary mb-1">
                      Timezone *
                    </label>
                    <select
                      {...register('timezone')}
                      className={`input-primary w-full ${
                        errors.timezone ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                      }`}
                    >
                      {timezones.map((timezone) => (
                        <option key={timezone.value} value={timezone.value}>
                          {timezone.label}
                        </option>
                      ))}
                    </select>
                    {errors.timezone && (
                      <p className="mt-1 text-sm text-error-600">{errors.timezone.message}</p>
                    )}
                  </div>

                  {/* Description */}
                  <div className="md:col-span-2">
                    <label htmlFor="description" className="block text-sm font-medium text-text-primary mb-1">
                      Description
                    </label>
                    <textarea
                      {...register('description')}
                      rows={3}
                      className="input-primary w-full"
                      placeholder="Brief description of your business"
                    />
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div>
                <h2 className="text-lg font-medium text-text-primary mb-4 flex items-center">
                  <EnvelopeIcon className="h-5 w-5 mr-2" />
                  Contact Information
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Website */}
                  <div>
                    <label htmlFor="website" className="block text-sm font-medium text-text-primary mb-1">
                      Website
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <GlobeAltIcon className="h-4 w-4 text-text-tertiary" />
                      </div>
                      <input
                        {...register('website')}
                        type="url"
                        className={`input-primary w-full pl-10 ${
                          errors.website ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                        }`}
                        placeholder="https://example.com"
                      />
                    </div>
                    {errors.website && (
                      <p className="mt-1 text-sm text-error-600">{errors.website.message}</p>
                    )}
                  </div>

                  {/* Phone */}
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-text-primary mb-1">
                      Phone
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <PhoneIcon className="h-4 w-4 text-text-tertiary" />
                      </div>
                      <input
                        {...register('phone')}
                        type="tel"
                        className="input-primary w-full pl-10"
                        placeholder="+****************"
                      />
                    </div>
                  </div>

                  {/* Email */}
                  <div className="md:col-span-2">
                    <label htmlFor="email" className="block text-sm font-medium text-text-primary mb-1">
                      Email
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <EnvelopeIcon className="h-4 w-4 text-text-tertiary" />
                      </div>
                      <input
                        {...register('email')}
                        type="email"
                        className={`input-primary w-full pl-10 ${
                          errors.email ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                        }`}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    {errors.email && (
                      <p className="mt-1 text-sm text-error-600">{errors.email.message}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Address Information */}
              <div>
                <h2 className="text-lg font-medium text-text-primary mb-4 flex items-center">
                  <MapPinIcon className="h-5 w-5 mr-2" />
                  Address Information
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Address */}
                  <div className="md:col-span-2">
                    <label htmlFor="address" className="block text-sm font-medium text-text-primary mb-1">
                      Street Address
                    </label>
                    <input
                      {...register('address')}
                      type="text"
                      className="input-primary w-full"
                      placeholder="123 Main Street"
                    />
                  </div>

                  {/* City */}
                  <div>
                    <label htmlFor="city" className="block text-sm font-medium text-text-primary mb-1">
                      City
                    </label>
                    <input
                      {...register('city')}
                      type="text"
                      className="input-primary w-full"
                      placeholder="New York"
                    />
                  </div>

                  {/* Country */}
                  <div>
                    <label htmlFor="country" className="block text-sm font-medium text-text-primary mb-1">
                      Country
                    </label>
                    <input
                      {...register('country')}
                      type="text"
                      className="input-primary w-full"
                      placeholder="United States"
                    />
                  </div>
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-border-primary">
                <Link
                  href="/businesses"
                  className="btn-secondary flex-1 text-center"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="btn-primary flex-1 flex items-center justify-center"
                >
                  {isSubmitting ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Creating...
                    </>
                  ) : (
                    'Create Business'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </Layout>
    </>
  );
}