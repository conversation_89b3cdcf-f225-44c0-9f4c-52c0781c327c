import { useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '../hooks/useAuth';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import {
  CurrencyDollarIcon,
  CreditCardIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  DevicePhoneMobileIcon,
  CloudIcon,
  ArrowRightIcon,
  CheckIcon,
} from '@heroicons/react/24/outline';

const features = [
  {
    name: 'Subscription Management',
    description: 'Track all your business subscriptions in one place with automated renewal reminders.',
    icon: CreditCardIcon,
  },
  {
    name: 'Expense Tracking',
    description: 'Monitor spending across multiple businesses and get detailed financial insights.',
    icon: CurrencyDollarIcon,
  },
  {
    name: 'Advanced Analytics',
    description: 'Generate comprehensive reports and visualize your spending patterns over time.',
    icon: ChartBarIcon,
  },
  {
    name: 'Secure & Private',
    description: 'Your financial data is encrypted and stored securely with enterprise-grade security.',
    icon: ShieldCheckIcon,
  },
  {
    name: 'Mobile Friendly',
    description: 'Access your dashboard from anywhere with our responsive web application.',
    icon: DevicePhoneMobileIcon,
  },
  {
    name: 'Cloud Sync',
    description: 'Your data is automatically synced across all devices and backed up securely.',
    icon: CloudIcon,
  },
];

const pricingPlans = [
  {
    name: 'Starter',
    price: 'Free',
    description: 'Perfect for individuals and small businesses',
    features: [
      'Up to 5 subscriptions',
      'Basic reporting',
      'Email support',
      'Mobile access',
    ],
    cta: 'Get Started',
    popular: false,
  },
  {
    name: 'Professional',
    price: '$9.99/month',
    description: 'Ideal for growing businesses',
    features: [
      'Unlimited subscriptions',
      'Advanced analytics',
      'Priority support',
      'Team collaboration',
      'Custom categories',
      'Export capabilities',
    ],
    cta: 'Start Free Trial',
    popular: true,
  },
  {
    name: 'Enterprise',
    price: 'Custom',
    description: 'For large organizations',
    features: [
      'Everything in Professional',
      'Custom integrations',
      'Dedicated support',
      'Advanced security',
      'Custom reporting',
      'SLA guarantee',
    ],
    cta: 'Contact Sales',
    popular: false,
  },
];

export default function HomePage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && user) {
      router.push('/dashboard');
    }
  }, [user, isLoading, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background-primary">
        <LoadingSpinner size="lg" text="Loading..." />
      </div>
    );
  }

  if (user) {
    return null; // Will redirect to dashboard
  }

  return (
    <>
      <Head>
        <title>SubDash - Subscription Management Made Simple</title>
        <meta name="description" content="Manage all your business subscriptions, track expenses, and get insights with SubDash - the ultimate subscription management platform." />
        <meta name="keywords" content="subscription management, expense tracking, business analytics, financial dashboard" />
      </Head>

      <div className="min-h-screen bg-background-primary">
        {/* Navigation */}
        <nav className="bg-white dark:bg-gray-900 shadow-sm border-b border-border-primary">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <h1 className="text-2xl font-bold text-primary-600">SubDash</h1>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Link href="/auth/signin" className="text-text-secondary hover:text-text-primary transition-colors">
                  Sign In
                </Link>
                <Link href="/auth/signup" className="btn-primary">
                  Get Started
                </Link>
              </div>
            </div>
          </div>
        </nav>

        {/* Hero Section */}
        <div className="relative overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div className="text-center">
              <h1 className="text-4xl md:text-6xl font-bold text-text-primary mb-6">
                Subscription Management
                <span className="block text-primary-600">Made Simple</span>
              </h1>
              <p className="text-xl text-text-secondary mb-8 max-w-3xl mx-auto">
                Take control of your business subscriptions, track expenses across multiple companies, 
                and get powerful insights to optimize your spending.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth/signup" className="btn-primary btn-lg">
                  Start Free Trial
                  <ArrowRightIcon className="h-5 w-5 ml-2" />
                </Link>
                <Link href="#features" className="btn-secondary btn-lg">
                  Learn More
                </Link>
              </div>
              <p className="text-sm text-text-tertiary mt-4">
                No credit card required • 14-day free trial • Cancel anytime
              </p>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div id="features" className="py-24 bg-background-secondary">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-text-primary mb-4">
                Everything you need to manage subscriptions
              </h2>
              <p className="text-xl text-text-secondary max-w-2xl mx-auto">
                Powerful features designed to help businesses of all sizes take control of their recurring expenses.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature) => {
                const Icon = feature.icon;
                return (
                  <div key={feature.name} className="card hover:shadow-lg transition-shadow">
                    <div className="h-12 w-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mb-4">
                      <Icon className="h-6 w-6 text-primary-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-text-primary mb-2">{feature.name}</h3>
                    <p className="text-text-secondary">{feature.description}</p>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Pricing Section */}
        <div className="py-24">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-text-primary mb-4">
                Simple, transparent pricing
              </h2>
              <p className="text-xl text-text-secondary max-w-2xl mx-auto">
                Choose the plan that's right for your business. Upgrade or downgrade at any time.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {pricingPlans.map((plan) => (
                <div
                  key={plan.name}
                  className={`card relative ${
                    plan.popular
                      ? 'border-primary-500 shadow-lg scale-105'
                      : 'border-border-primary'
                  }`}
                >
                  {plan.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <span className="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                        Most Popular
                      </span>
                    </div>
                  )}
                  <div className="text-center mb-6">
                    <h3 className="text-2xl font-bold text-text-primary mb-2">{plan.name}</h3>
                    <div className="text-4xl font-bold text-primary-600 mb-2">{plan.price}</div>
                    <p className="text-text-secondary">{plan.description}</p>
                  </div>
                  <ul className="space-y-3 mb-8">
                    {plan.features.map((feature) => (
                      <li key={feature} className="flex items-center">
                        <CheckIcon className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                        <span className="text-text-secondary">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Link
                    href="/auth/signup"
                    className={`block w-full text-center py-3 px-4 rounded-lg font-medium transition-colors ${
                      plan.popular
                        ? 'bg-primary-600 text-white hover:bg-primary-700'
                        : 'bg-background-secondary text-text-primary hover:bg-background-tertiary border border-border-primary'
                    }`}
                  >
                    {plan.cta}
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="py-24 bg-primary-600">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Ready to take control of your subscriptions?
            </h2>
            <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
              Join thousands of businesses that trust SubDash to manage their recurring expenses.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/signup" className="bg-white text-primary-600 hover:bg-gray-50 px-8 py-3 rounded-lg font-medium transition-colors">
                Start Your Free Trial
              </Link>
              <Link href="/auth/signin" className="border border-primary-300 text-white hover:bg-primary-700 px-8 py-3 rounded-lg font-medium transition-colors">
                Sign In
              </Link>
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-background-secondary border-t border-border-primary">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="col-span-1 md:col-span-2">
                <h3 className="text-2xl font-bold text-primary-600 mb-4">SubDash</h3>
                <p className="text-text-secondary mb-4">
                  The ultimate subscription management platform for businesses of all sizes.
                </p>
                <p className="text-text-tertiary text-sm">
                  © 2024 SubDash. All rights reserved.
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-text-primary mb-4">Product</h4>
                <ul className="space-y-2">
                  <li><a href="#features" className="text-text-secondary hover:text-text-primary transition-colors">Features</a></li>
                  <li><a href="#pricing" className="text-text-secondary hover:text-text-primary transition-colors">Pricing</a></li>
                  <li><Link href="/auth/signup" className="text-text-secondary hover:text-text-primary transition-colors">Sign Up</Link></li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-text-primary mb-4">Support</h4>
                <ul className="space-y-2">
                  <li><a href="#" className="text-text-secondary hover:text-text-primary transition-colors">Help Center</a></li>
                  <li><a href="#" className="text-text-secondary hover:text-text-primary transition-colors">Contact Us</a></li>
                  <li><a href="#" className="text-text-secondary hover:text-text-primary transition-colors">Privacy Policy</a></li>
                  <li><a href="#" className="text-text-secondary hover:text-text-primary transition-colors">Terms of Service</a></li>
                </ul>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}