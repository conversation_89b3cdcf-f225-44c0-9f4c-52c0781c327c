import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useQuery, useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { api } from '../../lib/api';
import { formatCurrency } from '../../lib/utils';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  TagIcon,
  PhotoIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';

// Form validation schema
const reimbursementSchema = z.object({
  business_id: z.string().min(1, 'Business is required'),
  description: z.string().min(1, 'Description is required').max(500, 'Description must be less than 500 characters'),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  currency: z.string().min(1, 'Currency is required'),
  expense_date: z.string().min(1, 'Expense date is required'),
  category: z.string().optional(),
  notes: z.string().optional(),
  receipt_url: z.string().optional(),
});

type ReimbursementFormData = z.infer<typeof reimbursementSchema>;

// Common expense categories
const expenseCategories = [
  'Software & Tools',
  'Marketing & Advertising',
  'Office Supplies',
  'Travel & Transportation',
  'Meals & Entertainment',
  'Professional Services',
  'Training & Education',
  'Equipment & Hardware',
  'Utilities',
  'Other',
];

// Common currencies
const currencies = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
];

export default function NewReimbursementPage() {
  const router = useRouter();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<ReimbursementFormData>({
    resolver: zodResolver(reimbursementSchema),
    defaultValues: {
      currency: 'USD',
      expense_date: new Date().toISOString().split('T')[0],
      category: 'Software & Tools',
    },
  });

  const watchedAmount = watch('amount');
  const watchedCurrency = watch('currency');
  const watchedBusinessId = watch('business_id');

  // Fetch businesses
  const { data: businesses = [], isLoading: isLoadingBusinesses } = useQuery({
    queryKey: ['businesses'],
    queryFn: () => api.businesses.getAll(),
  });

  // Create reimbursement mutation
  const createMutation = useMutation({
    mutationFn: (data: ReimbursementFormData) => api.reimbursements.create(data),
    onSuccess: (data: any) => {
      toast.success('Reimbursement request submitted successfully!');
      router.push(`/reimbursements/${data.id}`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to submit reimbursement request');
    },
  });

  // File upload mutation
  const uploadMutation = useMutation({
    mutationFn: (file: File) => api.files.upload(file),
    onSuccess: (data: any) => {
      setValue('receipt_url', data.url);
      toast.success('Receipt uploaded successfully!');
      setIsUploading(false);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to upload receipt');
      setIsUploading(false);
    },
  });

  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please upload a valid image (JPEG, PNG, GIF) or PDF file');
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }

    setUploadedFile(file);
    setIsUploading(true);

    // Create preview for images
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => setPreviewUrl(e.target?.result as string);
      reader.readAsDataURL(file);
    } else {
      setPreviewUrl(null);
    }

    // Upload file
    uploadMutation.mutate(file);
  };

  // Remove uploaded file
  const removeFile = () => {
    setUploadedFile(null);
    setPreviewUrl(null);
    setValue('receipt_url', '');
  };

  const onSubmit = (data: ReimbursementFormData) => {
    createMutation.mutate(data);
  };

  // Get selected business details
  const selectedBusiness = businesses.find((b: any) => b.id === watchedBusinessId);

  if (isLoadingBusinesses) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading..." />
        </div>
      </Layout>
    );
  }

  // Show message if no businesses found
  if (businesses.length === 0) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto">
          <div className="text-center py-12">
            <BuildingOfficeIcon className="h-12 w-12 text-text-tertiary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text-primary mb-2">No Businesses Found</h3>
            <p className="text-text-secondary mb-6">
              You need to create a business before you can submit reimbursement requests.
            </p>
            <Link href="/businesses/new" className="btn-primary">
              Create Your First Business
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Submit Reimbursement Request - SubDash</title>
        <meta name="description" content="Submit a new reimbursement request" />
      </Head>

      <Layout>
        <div className="max-w-2xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex items-center space-x-4">
            <Link
              href="/reimbursements"
              className="p-2 hover:bg-background-secondary rounded-lg transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5 text-text-secondary" />
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-text-primary">Submit Reimbursement Request</h1>
              <p className="text-sm text-text-secondary mt-1">
                Request reimbursement for business expenses
              </p>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="card">
              <h2 className="text-lg font-semibold text-text-primary mb-4 flex items-center">
                <DocumentTextIcon className="h-5 w-5 mr-2" />
                Basic Information
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Business *
                  </label>
                  <select
                    {...register('business_id')}
                    className={`input-primary w-full ${
                      errors.business_id ? 'border-error-500 focus:border-error-500' : ''
                    }`}
                  >
                    <option value="">Select a business</option>
                    {businesses.map((business: any) => (
                      <option key={business.id} value={business.id}>
                        {business.name}
                      </option>
                    ))}
                  </select>
                  {errors.business_id && (
                    <p className="text-error-600 text-sm mt-1">{errors.business_id.message}</p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Description *
                  </label>
                  <textarea
                    {...register('description')}
                    rows={3}
                    placeholder="Describe the expense (e.g., Monthly Slack subscription for team communication)"
                    className={`input-primary w-full ${
                      errors.description ? 'border-error-500 focus:border-error-500' : ''
                    }`}
                  />
                  {errors.description && (
                    <p className="text-error-600 text-sm mt-1">{errors.description.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Amount *
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    {...register('amount', { valueAsNumber: true })}
                    placeholder="0.00"
                    className={`input-primary w-full ${
                      errors.amount ? 'border-error-500 focus:border-error-500' : ''
                    }`}
                  />
                  {errors.amount && (
                    <p className="text-error-600 text-sm mt-1">{errors.amount.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Currency *
                  </label>
                  <select
                    {...register('currency')}
                    className={`input-primary w-full ${
                      errors.currency ? 'border-error-500 focus:border-error-500' : ''
                    }`}
                  >
                    {currencies.map((currency) => (
                      <option key={currency.code} value={currency.code}>
                        {currency.code} - {currency.name}
                      </option>
                    ))}
                  </select>
                  {errors.currency && (
                    <p className="text-error-600 text-sm mt-1">{errors.currency.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Expense Date *
                  </label>
                  <input
                    type="date"
                    {...register('expense_date')}
                    className={`input-primary w-full ${
                      errors.expense_date ? 'border-error-500 focus:border-error-500' : ''
                    }`}
                  />
                  {errors.expense_date && (
                    <p className="text-error-600 text-sm mt-1">{errors.expense_date.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Category
                  </label>
                  <select
                    {...register('category')}
                    className="input-primary w-full"
                  >
                    {expenseCategories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Receipt Upload */}
            <div className="card">
              <h2 className="text-lg font-semibold text-text-primary mb-4 flex items-center">
                <PhotoIcon className="h-5 w-5 mr-2" />
                Receipt Upload
              </h2>
              
              <div className="space-y-4">
                <div className="border-2 border-dashed border-border-secondary rounded-lg p-6 text-center">
                  {uploadedFile ? (
                    <div className="space-y-4">
                      {previewUrl && (
                        <div className="max-w-xs mx-auto">
                          <img
                            src={previewUrl}
                            alt="Receipt preview"
                            className="w-full h-auto rounded-lg shadow-sm"
                          />
                        </div>
                      )}
                      <div>
                        <p className="text-sm font-medium text-text-primary">{uploadedFile.name}</p>
                        <p className="text-xs text-text-secondary">
                          {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                      <button
                        type="button"
                        onClick={removeFile}
                        className="text-error-600 hover:text-error-700 text-sm font-medium"
                      >
                        Remove
                      </button>
                    </div>
                  ) : (
                    <div>
                      <PhotoIcon className="h-8 w-8 text-text-tertiary mx-auto mb-2" />
                      <p className="text-sm text-text-primary mb-1">Upload receipt (optional)</p>
                      <p className="text-xs text-text-secondary mb-4">
                        Supports JPEG, PNG, GIF, PDF up to 5MB
                      </p>
                      <label className="btn-secondary cursor-pointer">
                        Choose File
                        <input
                          type="file"
                          accept="image/*,.pdf"
                          onChange={handleFileUpload}
                          className="hidden"
                          disabled={isUploading}
                        />
                      </label>
                    </div>
                  )}
                  
                  {isUploading && (
                    <div className="mt-4">
                      <LoadingSpinner size="sm" text="Uploading..." />
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div className="card">
              <h2 className="text-lg font-semibold text-text-primary mb-4 flex items-center">
                <InformationCircleIcon className="h-5 w-5 mr-2" />
                Additional Information
              </h2>
              
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Notes
                </label>
                <textarea
                  {...register('notes')}
                  rows={3}
                  placeholder="Any additional notes or context for this expense..."
                  className="input-primary w-full"
                />
              </div>
            </div>

            {/* Preview */}
            {watchedAmount && watchedCurrency && (
              <div className="card bg-background-secondary">
                <h3 className="text-lg font-semibold text-text-primary mb-4">Request Preview</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Business:</span>
                    <span className="text-text-primary font-medium">
                      {selectedBusiness?.name || 'Not selected'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Amount:</span>
                    <span className="text-text-primary font-medium">
                      {formatCurrency(watchedAmount, watchedCurrency)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Status:</span>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-warning-600 bg-warning-100 dark:bg-warning-900">
                      Pending Review
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex flex-col sm:flex-row gap-3 sm:justify-end">
              <Link href="/reimbursements" className="btn-secondary">
                Cancel
              </Link>
              <button
                type="submit"
                disabled={isSubmitting || createMutation.isPending}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting || createMutation.isPending ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Submitting...
                  </>
                ) : (
                  'Submit Request'
                )}
              </button>
            </div>
          </form>
        </div>
      </Layout>
    </>
  );
}