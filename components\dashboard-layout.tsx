"use client"
import { useState, useEffect } from "react"
import { Bell, CreditCard, FileText, TrendingUp, Wallet, Receipt } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useCurrencyRates } from "@/hooks/useCurrencyRates"
import { SharedLayout } from "@/components/shared-layout"
import { DashboardLayoutProps, DashboardData, Subscription, PaymentRecord, ReimbursementRecord } from "@/types"
import { Storage } from "@/lib/storage"

// Calculate real dashboard statistics from stored data
function calculateDashboardData(
  subscriptions: Subscription[],
  payments: PaymentRecord[],
  reimbursements: ReimbursementRecord[],
  rates: { usdToKwd: number; gbpToKwd: number }
): DashboardData {
  // Active subscriptions count
  const activeSubscriptions = subscriptions.filter(sub => sub.status === "active")

  // Calculate upcoming payments (next 30 days)
  const now = new Date()
  const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)

  const upcomingPayments = activeSubscriptions.filter(sub => {
    const nextPayment = new Date(sub.nextPaymentDate)
    return nextPayment >= now && nextPayment <= thirtyDaysFromNow
  })

  const upcomingUSD = upcomingPayments
    .filter(sub => sub.currency === "USD")
    .reduce((sum, sub) => sum + sub.amount, 0)

  const upcomingGBP = upcomingPayments
    .filter(sub => sub.currency === "GBP")
    .reduce((sum, sub) => sum + sub.amount, 0)

  const upcomingKWD = (upcomingUSD * rates.usdToKwd) + (upcomingGBP * rates.gbpToKwd)

  // Pending reimbursements
  const pendingReimbursements = reimbursements
    .filter(reimb => reimb.status === "pending")
    .reduce((sum, reimb) => sum + reimb.kwdAmount, 0)

  // Total spent this month
  const currentMonth = now.getMonth()
  const currentYear = now.getFullYear()

  const thisMonthPayments = payments.filter(payment => {
    const paymentDate = new Date(payment.paymentDate)
    return paymentDate.getMonth() === currentMonth && paymentDate.getFullYear() === currentYear
  })

  const totalSpentThisMonth = thisMonthPayments.reduce((sum, payment) => sum + payment.kwdAmount, 0)

  // Determine status colors based on values
  const getUpcomingPaymentsStatus = () => {
    if (upcomingKWD > 200) return "warning"
    if (upcomingKWD > 100) return "neutral"
    return "success"
  }

  const getPendingReimbursementsStatus = () => {
    if (pendingReimbursements > 100) return "warning"
    if (pendingReimbursements > 0) return "success"
    return "neutral"
  }

  const getTotalSpentStatus = () => {
    if (totalSpentThisMonth > 1000) return "warning"
    if (totalSpentThisMonth > 500) return "neutral"
    return "success"
  }

  return {
    activeSubscriptions: {
      count: activeSubscriptions.length,
      status: activeSubscriptions.length > 0 ? "active" : "neutral"
    },
    upcomingPayments: {
      usd: upcomingUSD,
      gbp: upcomingGBP,
      kwd: upcomingKWD,
      status: getUpcomingPaymentsStatus(),
    },
    pendingReimbursements: {
      amount: pendingReimbursements,
      status: getPendingReimbursementsStatus()
    },
    totalSpentThisMonth: {
      amount: totalSpentThisMonth,
      status: getTotalSpentStatus()
    },
  }
}

function getStatusBorderColor(status: string) {
  switch (status) {
    case "success":
      return "border-l-green-500 dark:border-l-green-400"
    case "warning":
      return "border-l-yellow-500 dark:border-l-yellow-400"
    case "error":
      return "border-l-red-500 dark:border-l-red-400"
    case "active":
      return "border-l-blue-500 dark:border-l-blue-400"
    default:
      return "border-l-gray-300 dark:border-l-gray-600"
  }
}

function DashboardContent() {
  const { rates } = useCurrencyRates()
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    activeSubscriptions: { count: 0, status: "neutral" },
    upcomingPayments: { usd: 0, gbp: 0, kwd: 0, status: "neutral" },
    pendingReimbursements: { amount: 0, status: "neutral" },
    totalSpentThisMonth: { amount: 0, status: "neutral" },
  })

  useEffect(() => {
    // Load real data from localStorage
    const subscriptions = Storage.subscriptions.get()
    const payments = Storage.payments.get()
    const reimbursements = Storage.reimbursements.get()

    // Calculate dashboard statistics
    const calculatedData = calculateDashboardData(subscriptions, payments, reimbursements, rates)
    setDashboardData(calculatedData)
  }, [rates])

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6 dark:bg-[#28282B]">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">Welcome back! Here's your subscription overview.</p>
        </div>
        <Button className="dark:bg-blue-600 dark:hover:bg-blue-700">
          <Bell className="mr-2 h-4 w-4" />
          Notifications
        </Button>
      </div>

      <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
        <Card
          className={`shadow-md hover:shadow-lg transition-shadow duration-200 border-l-4 ${getStatusBorderColor(dashboardData.activeSubscriptions.status)} dark:bg-[#28282B] dark:border-[#3a3a3d] dark:shadow-xl dark:hover:shadow-2xl`}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3">
            <div className="text-2xl font-bold">{dashboardData.activeSubscriptions.count}</div>
            <p className="text-xs text-muted-foreground">
              {dashboardData.activeSubscriptions.count === 0 ? "No active subscriptions" : "Currently active"}
            </p>
          </CardContent>
        </Card>

        <Card
          className={`shadow-md hover:shadow-lg transition-shadow duration-200 border-l-4 ${getStatusBorderColor(dashboardData.upcomingPayments.status)} dark:bg-[#28282B] dark:border-[#3a3a3d] dark:shadow-xl dark:hover:shadow-2xl`}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Payments</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3">
            <div className="space-y-2">
              {/* Primary KWD Display */}
              <div className="text-2xl font-bold text-blue-700 dark:text-blue-400">
                {(
                  dashboardData.upcomingPayments.usd * rates.usdToKwd +
                  dashboardData.upcomingPayments.gbp * rates.gbpToKwd
                ).toFixed(2)}{" "}
                KWD
              </div>

              {/* Foreign Currency Breakdown */}
              <div className="text-xs text-muted-foreground space-y-1 border-t pt-2 dark:border-t-[#3a3a3d]">
                <div className="flex justify-between">
                  <span className="font-semibold">
                    {(dashboardData.upcomingPayments.usd * rates.usdToKwd).toFixed(2)} KWD
                  </span>
                  <span className="text-gray-500 dark:text-gray-400">
                    (${dashboardData.upcomingPayments.usd.toFixed(2)} USD)
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-semibold">
                    {(dashboardData.upcomingPayments.gbp * rates.gbpToKwd).toFixed(2)} KWD
                  </span>
                  <span className="text-gray-500 dark:text-gray-400">
                    (£{dashboardData.upcomingPayments.gbp.toFixed(2)} GBP)
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card
          className={`shadow-md hover:shadow-lg transition-shadow duration-200 border-l-4 ${getStatusBorderColor(dashboardData.pendingReimbursements.status)} dark:bg-[#28282B] dark:border-[#3a3a3d] dark:shadow-xl dark:hover:shadow-2xl`}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Reimbursements</CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3">
            <div className="text-2xl font-bold text-green-700 dark:text-green-400">
              {dashboardData.pendingReimbursements.amount.toFixed(2)} KWD
            </div>
            <p className="text-xs text-muted-foreground">
              {dashboardData.pendingReimbursements.amount === 0 ? "No pending requests" : "Pending requests (KWD only)"}
            </p>
          </CardContent>
        </Card>

        <Card
          className={`shadow-md hover:shadow-lg transition-shadow duration-200 border-l-4 ${getStatusBorderColor(dashboardData.totalSpentThisMonth.status)} dark:bg-[#28282B] dark:border-[#3a3a3d] dark:shadow-xl dark:hover:shadow-2xl`}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spent This Month</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3">
            <div className="text-2xl font-bold text-purple-700 dark:text-purple-400">
              {dashboardData.totalSpentThisMonth.amount.toFixed(2)} KWD
            </div>
            <p className="text-xs text-muted-foreground">
              {dashboardData.totalSpentThisMonth.amount === 0 ? "No spending this month" : "Current month total"}
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4 dark:bg-[#28282B] dark:border-[#3a3a3d] dark:shadow-xl">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Your latest subscription activities and payments with currency details.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Storage.payments.get().length === 0 && Storage.subscriptions.get().length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-muted-foreground">
                    <CreditCard className="mx-auto h-12 w-12 mb-4 opacity-50" />
                    <p className="text-sm">No recent activity</p>
                    <p className="text-xs">Start by adding your first subscription</p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-muted-foreground">
                    <FileText className="mx-auto h-12 w-12 mb-4 opacity-50" />
                    <p className="text-sm">Recent activity will appear here</p>
                    <p className="text-xs">Based on your subscription payments and updates</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-3 dark:bg-[#28282B] dark:border-[#3a3a3d] dark:shadow-xl">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Manage your subscriptions efficiently.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button className="w-full justify-start bg-transparent dark:hover:bg-[#3a3a3d]" variant="outline">
              <CreditCard className="mr-2 h-4 w-4" />
              Add New Subscription
            </Button>
            <Button className="w-full justify-start bg-transparent dark:hover:bg-[#3a3a3d]" variant="outline">
              <Receipt className="mr-2 h-4 w-4" />
              Submit Reimbursement
            </Button>
            <Button className="w-full justify-start bg-transparent dark:hover:bg-[#3a3a3d]" variant="outline">
              <FileText className="mr-2 h-4 w-4" />
              Generate Report
            </Button>
            <Button className="w-full justify-start bg-transparent dark:hover:bg-[#3a3a3d]" variant="outline">
              <TrendingUp className="mr-2 h-4 w-4" />
              View Currency Trends
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export function DashboardLayout({ activeUrl = "/" }: DashboardLayoutProps) {
  return (
    <SharedLayout activeUrl={activeUrl}>
      <DashboardContent />
    </SharedLayout>
  )
}
