import { useState, useMemo } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import ConfirmationModal from '../../components/ui/ConfirmationModal';
import { api } from '../../lib/api';
import { formatCurrency, formatDate, formatRelativeTime } from '../../lib/utils';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowsUpDownIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  DocumentTextIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  BanknotesIcon,
} from '@heroicons/react/24/outline';
import {
  CheckCircleIcon as CheckCircleIconSolid,
  ClockIcon as ClockIconSolid,
  XCircleIcon as XCircleIconSolid,
  ExclamationTriangleIcon as ExclamationTriangleIconSolid,
  BanknotesIcon as BanknotesIconSolid,
} from '@heroicons/react/24/solid';

// Reimbursement status configurations
const statusConfig = {
  pending: {
    label: 'Pending',
    icon: ClockIconSolid,
    className: 'text-warning-600 bg-warning-100 dark:bg-warning-900',
    description: 'Awaiting approval',
  },
  approved: {
    label: 'Approved',
    icon: CheckCircleIconSolid,
    className: 'text-success-600 bg-success-100 dark:bg-success-900',
    description: 'Approved for payment',
  },
  rejected: {
    label: 'Rejected',
    icon: XCircleIconSolid,
    className: 'text-error-600 bg-error-100 dark:bg-error-900',
    description: 'Request rejected',
  },
  paid: {
    label: 'Paid',
    icon: BanknotesIconSolid,
    className: 'text-primary-600 bg-primary-100 dark:bg-primary-900',
    description: 'Payment completed',
  },
};

export default function ReimbursementsPage() {
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [businessFilter, setBusinessFilter] = useState('all');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [deleteReimbursementId, setDeleteReimbursementId] = useState<string | null>(null);
  const [actionReimbursementId, setActionReimbursementId] = useState<string | null>(null);
  const [actionType, setActionType] = useState<'approve' | 'reject' | 'mark_paid' | null>(null);

  // Fetch reimbursements
  const { data: reimbursements = [], isLoading } = useQuery({
    queryKey: ['reimbursements'],
    queryFn: () => api.reimbursements.getAll(),
  });

  // Fetch businesses for filter
  const { data: businesses = [] } = useQuery({
    queryKey: ['businesses'],
    queryFn: () => api.businesses.getAll(),
  });

  // Delete reimbursement mutation
  const deleteMutation = useMutation({
    mutationFn: (id: string) => api.reimbursements.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reimbursements'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Reimbursement deleted successfully!');
      setDeleteReimbursementId(null);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete reimbursement');
    },
  });

  // Approve reimbursement mutation
  const approveMutation = useMutation({
    mutationFn: (id: string) => api.reimbursements.approve(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reimbursements'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Reimbursement approved successfully!');
      setActionReimbursementId(null);
      setActionType(null);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to approve reimbursement');
    },
  });

  // Reject reimbursement mutation
  const rejectMutation = useMutation({
    mutationFn: (id: string) => api.reimbursements.reject(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reimbursements'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Reimbursement rejected successfully!');
      setActionReimbursementId(null);
      setActionType(null);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to reject reimbursement');
    },
  });

  // Mark as paid mutation
  const markPaidMutation = useMutation({
    mutationFn: (id: string) => api.reimbursements.markPaid(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reimbursements'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Reimbursement marked as paid successfully!');
      setActionReimbursementId(null);
      setActionType(null);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to mark reimbursement as paid');
    },
  });

  // Filter and sort reimbursements
  const filteredAndSortedReimbursements = useMemo(() => {
    let filtered = reimbursements.filter((reimbursement: any) => {
      const matchesSearch = 
        reimbursement.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        reimbursement.business?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        reimbursement.amount.toString().includes(searchTerm) ||
        reimbursement.category?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || reimbursement.status === statusFilter;
      const matchesBusiness = businessFilter === 'all' || reimbursement.business_id === businessFilter;
      
      return matchesSearch && matchesStatus && matchesBusiness;
    });

    // Sort reimbursements
    filtered.sort((a: any, b: any) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];
      
      // Handle nested properties
      if (sortBy === 'business_name') {
        aValue = a.business?.name || '';
        bValue = b.business?.name || '';
      }
      
      // Handle different data types
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [reimbursements, searchTerm, statusFilter, businessFilter, sortBy, sortOrder]);

  // Calculate summary statistics
  const stats = useMemo(() => {
    const total = reimbursements.length;
    const pending = reimbursements.filter((r: any) => r.status === 'pending').length;
    const approved = reimbursements.filter((r: any) => r.status === 'approved').length;
    const rejected = reimbursements.filter((r: any) => r.status === 'rejected').length;
    const paid = reimbursements.filter((r: any) => r.status === 'paid').length;
    const totalAmount = reimbursements
      .filter((r: any) => r.status === 'paid')
      .reduce((sum: number, r: any) => sum + r.amount, 0);
    const pendingAmount = reimbursements
      .filter((r: any) => r.status === 'pending' || r.status === 'approved')
      .reduce((sum: number, r: any) => sum + r.amount, 0);
    
    return { total, pending, approved, rejected, paid, totalAmount, pendingAmount };
  }, [reimbursements]);

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleAction = (id: string, action: 'approve' | 'reject' | 'mark_paid') => {
    setActionReimbursementId(id);
    setActionType(action);
  };

  const confirmAction = () => {
    if (!actionReimbursementId || !actionType) return;
    
    switch (actionType) {
      case 'approve':
        approveMutation.mutate(actionReimbursementId);
        break;
      case 'reject':
        rejectMutation.mutate(actionReimbursementId);
        break;
      case 'mark_paid':
        markPaidMutation.mutate(actionReimbursementId);
        break;
    }
  };

  const getBusinessName = (businessId: string) => {
    const business = businesses.find((b: any) => b.id === businessId);
    return business?.name || 'Unknown Business';
  };

  const getActionModalContent = () => {
    switch (actionType) {
      case 'approve':
        return {
          title: 'Approve Reimbursement',
          message: 'Are you sure you want to approve this reimbursement request?',
          confirmText: 'Approve',
          confirmVariant: 'primary' as const,
        };
      case 'reject':
        return {
          title: 'Reject Reimbursement',
          message: 'Are you sure you want to reject this reimbursement request? This action cannot be undone.',
          confirmText: 'Reject',
          confirmVariant: 'danger' as const,
        };
      case 'mark_paid':
        return {
          title: 'Mark as Paid',
          message: 'Are you sure you want to mark this reimbursement as paid?',
          confirmText: 'Mark as Paid',
          confirmVariant: 'primary' as const,
        };
      default:
        return {
          title: '',
          message: '',
          confirmText: '',
          confirmVariant: 'primary' as const,
        };
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading reimbursements..." />
        </div>
      </Layout>
    );
  }

  const actionModalContent = getActionModalContent();

  return (
    <>
      <Head>
        <title>Reimbursements - SubDash</title>
        <meta name="description" content="Manage your subscription reimbursements" />
      </Head>

      <Layout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-text-primary">Reimbursements</h1>
              <p className="text-sm text-text-secondary mt-1">
                Track and manage subscription reimbursement requests
              </p>
            </div>
            <Link href="/reimbursements/new" className="btn-primary flex items-center">
              <PlusIcon className="h-4 w-4 mr-2" />
              Submit Request
            </Link>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-4">
            <div className="card">
              <div className="flex items-center">
                <div className="p-2 bg-primary-100 dark:bg-primary-900 rounded-lg">
                  <DocumentTextIcon className="h-5 w-5 text-primary-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-text-secondary">Total</p>
                  <p className="text-lg font-semibold text-text-primary">{stats.total}</p>
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="flex items-center">
                <div className="p-2 bg-warning-100 dark:bg-warning-900 rounded-lg">
                  <ClockIcon className="h-5 w-5 text-warning-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-text-secondary">Pending</p>
                  <p className="text-lg font-semibold text-text-primary">{stats.pending}</p>
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="flex items-center">
                <div className="p-2 bg-success-100 dark:bg-success-900 rounded-lg">
                  <CheckCircleIcon className="h-5 w-5 text-success-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-text-secondary">Approved</p>
                  <p className="text-lg font-semibold text-text-primary">{stats.approved}</p>
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="flex items-center">
                <div className="p-2 bg-error-100 dark:bg-error-900 rounded-lg">
                  <XCircleIcon className="h-5 w-5 text-error-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-text-secondary">Rejected</p>
                  <p className="text-lg font-semibold text-text-primary">{stats.rejected}</p>
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="flex items-center">
                <div className="p-2 bg-primary-100 dark:bg-primary-900 rounded-lg">
                  <BanknotesIcon className="h-5 w-5 text-primary-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-text-secondary">Paid</p>
                  <p className="text-lg font-semibold text-text-primary">{stats.paid}</p>
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="flex items-center">
                <div className="p-2 bg-success-100 dark:bg-success-900 rounded-lg">
                  <CurrencyDollarIcon className="h-5 w-5 text-success-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-text-secondary">Total Paid</p>
                  <p className="text-lg font-semibold text-text-primary">
                    {formatCurrency(stats.totalAmount, 'USD')}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="flex items-center">
                <div className="p-2 bg-warning-100 dark:bg-warning-900 rounded-lg">
                  <ExclamationTriangleIcon className="h-5 w-5 text-warning-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-text-secondary">Pending Amount</p>
                  <p className="text-lg font-semibold text-text-primary">
                    {formatCurrency(stats.pendingAmount, 'USD')}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="card">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary" />
                  <input
                    type="text"
                    placeholder="Search reimbursements..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="input-primary pl-10 w-full"
                  />
                </div>
              </div>
              
              {/* Filters */}
              <div className="flex flex-col sm:flex-row gap-3">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="input-primary min-w-[120px]"
                >
                  <option value="all">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                  <option value="paid">Paid</option>
                </select>
                
                <select
                  value={businessFilter}
                  onChange={(e) => setBusinessFilter(e.target.value)}
                  className="input-primary min-w-[150px]"
                >
                  <option value="all">All Businesses</option>
                  {businesses.map((business: any) => (
                    <option key={business.id} value={business.id}>
                      {business.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Reimbursements Table */}
          <div className="card overflow-hidden">
            {filteredAndSortedReimbursements.length === 0 ? (
              <div className="text-center py-12">
                <DocumentTextIcon className="h-12 w-12 text-text-tertiary mx-auto mb-4" />
                <h3 className="text-lg font-medium text-text-primary mb-2">No reimbursements found</h3>
                <p className="text-text-secondary mb-6">
                  {reimbursements.length === 0 
                    ? "You haven't submitted any reimbursement requests yet."
                    : "No reimbursements match your current filters."
                  }
                </p>
                {reimbursements.length === 0 && (
                  <Link href="/reimbursements/new" className="btn-primary">
                    Submit Your First Request
                  </Link>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-border-primary">
                  <thead className="bg-background-secondary">
                    <tr>
                      <th 
                        className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-background-tertiary"
                        onClick={() => handleSort('created_at')}
                      >
                        <div className="flex items-center">
                          Date
                          <ArrowsUpDownIcon className="h-4 w-4 ml-1" />
                        </div>
                      </th>
                      <th 
                        className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-background-tertiary"
                        onClick={() => handleSort('description')}
                      >
                        <div className="flex items-center">
                          Description
                          <ArrowsUpDownIcon className="h-4 w-4 ml-1" />
                        </div>
                      </th>
                      <th 
                        className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-background-tertiary"
                        onClick={() => handleSort('business_name')}
                      >
                        <div className="flex items-center">
                          Business
                          <ArrowsUpDownIcon className="h-4 w-4 ml-1" />
                        </div>
                      </th>
                      <th 
                        className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-background-tertiary"
                        onClick={() => handleSort('amount')}
                      >
                        <div className="flex items-center">
                          Amount
                          <ArrowsUpDownIcon className="h-4 w-4 ml-1" />
                        </div>
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Category
                      </th>
                      <th 
                        className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-background-tertiary"
                        onClick={() => handleSort('status')}
                      >
                        <div className="flex items-center">
                          Status
                          <ArrowsUpDownIcon className="h-4 w-4 ml-1" />
                        </div>
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-background-primary divide-y divide-border-primary">
                    {filteredAndSortedReimbursements.map((reimbursement: any) => {
                      const status = reimbursement.status as keyof typeof statusConfig;
                      const StatusIcon = statusConfig[status]?.icon || ClockIconSolid;
                      
                      return (
                        <tr key={reimbursement.id} className="hover:bg-background-secondary">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-text-primary">
                              {formatDate(reimbursement.created_at)}
                            </div>
                            <div className="text-xs text-text-secondary">
                              {formatRelativeTime(reimbursement.created_at)}
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="text-sm font-medium text-text-primary">
                              {reimbursement.description}
                            </div>
                            {reimbursement.receipt_url && (
                              <div className="text-xs text-text-secondary">
                                Receipt attached
                              </div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-text-primary">
                              {getBusinessName(reimbursement.business_id)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-text-primary">
                              {formatCurrency(reimbursement.amount, reimbursement.currency)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-text-primary capitalize">
                              {reimbursement.category || 'Other'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              statusConfig[status]?.className || 'text-text-secondary bg-background-secondary'
                            }`}>
                              <StatusIcon className="h-3 w-3 mr-1" />
                              {statusConfig[status]?.label || reimbursement.status}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex items-center justify-end space-x-2">
                              <Link
                                href={`/reimbursements/${reimbursement.id}`}
                                className="text-text-secondary hover:text-primary-600 transition-colors"
                                title="View details"
                              >
                                <EyeIcon className="h-4 w-4" />
                              </Link>
                              
                              {reimbursement.status === 'pending' && (
                                <>
                                  <button
                                    onClick={() => handleAction(reimbursement.id, 'approve')}
                                    className="text-text-secondary hover:text-success-600 transition-colors"
                                    title="Approve"
                                  >
                                    <CheckCircleIcon className="h-4 w-4" />
                                  </button>
                                  <button
                                    onClick={() => handleAction(reimbursement.id, 'reject')}
                                    className="text-text-secondary hover:text-error-600 transition-colors"
                                    title="Reject"
                                  >
                                    <XCircleIcon className="h-4 w-4" />
                                  </button>
                                </>
                              )}
                              
                              {reimbursement.status === 'approved' && (
                                <button
                                  onClick={() => handleAction(reimbursement.id, 'mark_paid')}
                                  className="text-text-secondary hover:text-primary-600 transition-colors"
                                  title="Mark as paid"
                                >
                                  <BanknotesIcon className="h-4 w-4" />
                                </button>
                              )}
                              
                              <Link
                                href={`/reimbursements/${reimbursement.id}/edit`}
                                className="text-text-secondary hover:text-primary-600 transition-colors"
                                title="Edit reimbursement"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </Link>
                              
                              <button
                                onClick={() => setDeleteReimbursementId(reimbursement.id)}
                                className="text-text-secondary hover:text-error-600 transition-colors"
                                title="Delete reimbursement"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Results Summary */}
          {filteredAndSortedReimbursements.length > 0 && (
            <div className="text-sm text-text-secondary text-center">
              Showing {filteredAndSortedReimbursements.length} of {reimbursements.length} reimbursements
            </div>
          )}
        </div>

        {/* Delete Confirmation Modal */}
        <ConfirmationModal
          isOpen={!!deleteReimbursementId}
          onClose={() => setDeleteReimbursementId(null)}
          onConfirm={() => deleteReimbursementId && deleteMutation.mutate(deleteReimbursementId)}
          title="Delete Reimbursement"
          message="Are you sure you want to delete this reimbursement? This action cannot be undone."
          confirmText="Delete"
          type="danger"
          loading={deleteMutation.isPending}
        />

        {/* Action Confirmation Modal */}
        <ConfirmationModal
          isOpen={!!actionReimbursementId && !!actionType}
          onClose={() => {
            setActionReimbursementId(null);
            setActionType(null);
          }}
          onConfirm={confirmAction}
          title={actionModalContent.title}
          message={actionModalContent.message}
          confirmText={actionModalContent.confirmText}
          type={actionModalContent.confirmVariant === 'primary' ? 'info' : 'danger'}
          loading={
            approveMutation.isPending || 
            rejectMutation.isPending || 
            markPaidMutation.isPending
          }
        />
      </Layout>
    </>
  );
}