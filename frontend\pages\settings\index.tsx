import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import ConfirmationModal from '../../components/ui/ConfirmationModal';
import { useAuth } from '../../hooks/useAuth';
import { useTheme } from '../../hooks/useTheme';
import { api } from '../../lib/api';
import {
  UserIcon,
  CogIcon,
  BellIcon,
  ShieldCheckIcon,
  PaintBrushIcon,
  GlobeAltIcon,
  EyeIcon,
  EyeSlashIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import {
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
} from '@heroicons/react/24/solid';

// Profile form schema
const profileSchema = z.object({
  first_name: z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters'),
  last_name: z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().optional(),
  timezone: z.string().min(1, 'Timezone is required'),
  currency: z.string().min(1, 'Currency is required'),
  language: z.string().min(1, 'Language is required'),
});

// Password form schema
const passwordSchema = z.object({
  current_password: z.string().min(1, 'Current password is required'),
  new_password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character'),
  confirm_password: z.string().min(1, 'Please confirm your password'),
}).refine((data) => data.new_password === data.confirm_password, {
  message: "Passwords don't match",
  path: ['confirm_password'],
});

// Notification settings schema
const notificationSchema = z.object({
  email_notifications: z.boolean(),
  push_notifications: z.boolean(),
  subscription_reminders: z.boolean(),
  payment_alerts: z.boolean(),
  weekly_reports: z.boolean(),
  marketing_emails: z.boolean(),
});

type ProfileFormData = z.infer<typeof profileSchema>;
type PasswordFormData = z.infer<typeof passwordSchema>;
type NotificationFormData = z.infer<typeof notificationSchema>;

// Available options
const timezones = [
  { value: 'UTC', label: 'UTC (Coordinated Universal Time)' },
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },
  { value: 'Europe/Paris', label: 'Central European Time (CET)' },
  { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },
  { value: 'Asia/Shanghai', label: 'China Standard Time (CST)' },
  { value: 'Australia/Sydney', label: 'Australian Eastern Time (AET)' },
];

const currencies = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
];

const languages = [
  { code: 'en', name: 'English' },
  { code: 'es', name: 'Spanish' },
  { code: 'fr', name: 'French' },
  { code: 'de', name: 'German' },
  { code: 'it', name: 'Italian' },
  { code: 'pt', name: 'Portuguese' },
  { code: 'zh', name: 'Chinese' },
  { code: 'ja', name: 'Japanese' },
];

const themeOptions = [
  { value: 'light', label: 'Light', icon: SunIcon },
  { value: 'dark', label: 'Dark', icon: MoonIcon },
  { value: 'system', label: 'System', icon: ComputerDesktopIcon },
];

export default function SettingsPage() {
  const { user, logout } = useAuth();
  const { theme, setTheme } = useTheme();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('profile');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Profile form
  const {
    register: registerProfile,
    handleSubmit: handleProfileSubmit,
    reset: resetProfile,
    formState: { errors: profileErrors, isSubmitting: isProfileSubmitting, isDirty: isProfileDirty },
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
  });

  // Password form
  const {
    register: registerPassword,
    handleSubmit: handlePasswordSubmit,
    reset: resetPassword,
    formState: { errors: passwordErrors, isSubmitting: isPasswordSubmitting },
  } = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
  });

  // Notification form
  const {
    register: registerNotification,
    handleSubmit: handleNotificationSubmit,
    reset: resetNotification,
    watch: watchNotification,
    formState: { isSubmitting: isNotificationSubmitting, isDirty: isNotificationDirty },
  } = useForm<NotificationFormData>({
    resolver: zodResolver(notificationSchema),
  });

  // Fetch user settings
  const { data: settings, isLoading } = useQuery({
    queryKey: ['user-settings'],
    queryFn: () => api.users.getSettings(),
  });

  // Update forms when settings load
  useEffect(() => {
    if (settings) {
      resetProfile({
        first_name: settings.first_name || '',
        last_name: settings.last_name || '',
        email: settings.email || '',
        phone: settings.phone || '',
        timezone: settings.timezone || 'UTC',
        currency: settings.currency || 'USD',
        language: settings.language || 'en',
      });
      
      resetNotification({
        email_notifications: settings.email_notifications ?? true,
        push_notifications: settings.push_notifications ?? true,
        subscription_reminders: settings.subscription_reminders ?? true,
        payment_alerts: settings.payment_alerts ?? true,
        weekly_reports: settings.weekly_reports ?? false,
        marketing_emails: settings.marketing_emails ?? false,
      });
    }
  }, [settings, resetProfile, resetNotification]);

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: (data: ProfileFormData) => api.users.updateProfile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-settings'] });
      queryClient.invalidateQueries({ queryKey: ['user'] });
      toast.success('Profile updated successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update profile');
    },
  });

  // Update password mutation
  const updatePasswordMutation = useMutation({
    mutationFn: (data: PasswordFormData) => api.users.updatePassword(data),
    onSuccess: () => {
      resetPassword();
      toast.success('Password updated successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update password');
    },
  });

  // Update notification settings mutation
  const updateNotificationMutation = useMutation({
    mutationFn: (data: NotificationFormData) => api.users.updateNotificationSettings(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-settings'] });
      toast.success('Notification settings updated successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update notification settings');
    },
  });

  // Delete account mutation
  const deleteAccountMutation = useMutation({
    mutationFn: () => api.users.deleteAccount(),
    onSuccess: () => {
      toast.success('Account deleted successfully');
      logout();
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete account');
    },
  });

  const onProfileSubmit = (data: ProfileFormData) => {
    updateProfileMutation.mutate(data);
  };

  const onPasswordSubmit = (data: PasswordFormData) => {
    updatePasswordMutation.mutate(data);
  };

  const onNotificationSubmit = (data: NotificationFormData) => {
    updateNotificationMutation.mutate(data);
  };

  const tabs = [
    { id: 'profile', name: 'Profile', icon: UserIcon },
    { id: 'security', name: 'Security', icon: ShieldCheckIcon },
    { id: 'notifications', name: 'Notifications', icon: BellIcon },
    { id: 'appearance', name: 'Appearance', icon: PaintBrushIcon },
    { id: 'preferences', name: 'Preferences', icon: CogIcon },
  ];

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading settings..." />
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Settings - SubDash</title>
        <meta name="description" content="Manage your account settings, preferences, and security options" />
      </Head>

      <Layout>
        <div className="max-w-6xl mx-auto space-y-6">
          {/* Header */}
          <div>
            <h1 className="text-2xl font-bold text-text-primary">Settings</h1>
            <p className="text-text-secondary mt-1">
              Manage your account settings and preferences
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                        activeTab === tab.id
                          ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
                          : 'text-text-secondary hover:text-text-primary hover:bg-background-secondary'
                      }`}
                    >
                      <Icon className="h-5 w-5 mr-3" />
                      {tab.name}
                    </button>
                  );
                })}
              </nav>
            </div>

            {/* Content */}
            <div className="lg:col-span-3">
              {activeTab === 'profile' && (
                <div className="card">
                  <h2 className="text-lg font-semibold text-text-primary mb-6 flex items-center">
                    <UserIcon className="h-5 w-5 mr-2" />
                    Profile Information
                  </h2>
                  
                  <form onSubmit={handleProfileSubmit(onProfileSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          First Name *
                        </label>
                        <input
                          type="text"
                          {...registerProfile('first_name')}
                          className={`input-primary w-full ${
                            profileErrors.first_name ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        />
                        {profileErrors.first_name && (
                          <p className="text-error-600 text-sm mt-1">{profileErrors.first_name.message}</p>
                        )}
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Last Name *
                        </label>
                        <input
                          type="text"
                          {...registerProfile('last_name')}
                          className={`input-primary w-full ${
                            profileErrors.last_name ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        />
                        {profileErrors.last_name && (
                          <p className="text-error-600 text-sm mt-1">{profileErrors.last_name.message}</p>
                        )}
                      </div>
                      
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Email Address *
                        </label>
                        <input
                          type="email"
                          {...registerProfile('email')}
                          className={`input-primary w-full ${
                            profileErrors.email ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        />
                        {profileErrors.email && (
                          <p className="text-error-600 text-sm mt-1">{profileErrors.email.message}</p>
                        )}
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Phone Number
                        </label>
                        <input
                          type="tel"
                          {...registerProfile('phone')}
                          className="input-primary w-full"
                          placeholder="+****************"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Timezone *
                        </label>
                        <select
                          {...registerProfile('timezone')}
                          className={`input-primary w-full ${
                            profileErrors.timezone ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        >
                          {timezones.map((tz) => (
                            <option key={tz.value} value={tz.value}>
                              {tz.label}
                            </option>
                          ))}
                        </select>
                        {profileErrors.timezone && (
                          <p className="text-error-600 text-sm mt-1">{profileErrors.timezone.message}</p>
                        )}
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Default Currency *
                        </label>
                        <select
                          {...registerProfile('currency')}
                          className={`input-primary w-full ${
                            profileErrors.currency ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        >
                          {currencies.map((currency) => (
                            <option key={currency.code} value={currency.code}>
                              {currency.code} - {currency.name}
                            </option>
                          ))}
                        </select>
                        {profileErrors.currency && (
                          <p className="text-error-600 text-sm mt-1">{profileErrors.currency.message}</p>
                        )}
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Language *
                        </label>
                        <select
                          {...registerProfile('language')}
                          className={`input-primary w-full ${
                            profileErrors.language ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        >
                          {languages.map((lang) => (
                            <option key={lang.code} value={lang.code}>
                              {lang.name}
                            </option>
                          ))}
                        </select>
                        {profileErrors.language && (
                          <p className="text-error-600 text-sm mt-1">{profileErrors.language.message}</p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex justify-end">
                      <button
                        type="submit"
                        disabled={isProfileSubmitting || updateProfileMutation.isPending || !isProfileDirty}
                        className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isProfileSubmitting || updateProfileMutation.isPending ? (
                          <>
                            <LoadingSpinner size="sm" className="mr-2" />
                            Updating...
                          </>
                        ) : (
                          'Save Changes'
                        )}
                      </button>
                    </div>
                  </form>
                </div>
              )}

              {activeTab === 'security' && (
                <div className="space-y-6">
                  {/* Change Password */}
                  <div className="card">
                    <h2 className="text-lg font-semibold text-text-primary mb-6 flex items-center">
                      <ShieldCheckIcon className="h-5 w-5 mr-2" />
                      Change Password
                    </h2>
                    
                    <form onSubmit={handlePasswordSubmit(onPasswordSubmit)} className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Current Password *
                        </label>
                        <div className="relative">
                          <input
                            type={showCurrentPassword ? 'text' : 'password'}
                            {...registerPassword('current_password')}
                            className={`input-primary w-full pr-10 ${
                              passwordErrors.current_password ? 'border-error-500 focus:border-error-500' : ''
                            }`}
                          />
                          <button
                            type="button"
                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          >
                            {showCurrentPassword ? (
                              <EyeSlashIcon className="h-4 w-4 text-text-tertiary" />
                            ) : (
                              <EyeIcon className="h-4 w-4 text-text-tertiary" />
                            )}
                          </button>
                        </div>
                        {passwordErrors.current_password && (
                          <p className="text-error-600 text-sm mt-1">{passwordErrors.current_password.message}</p>
                        )}
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          New Password *
                        </label>
                        <div className="relative">
                          <input
                            type={showNewPassword ? 'text' : 'password'}
                            {...registerPassword('new_password')}
                            className={`input-primary w-full pr-10 ${
                              passwordErrors.new_password ? 'border-error-500 focus:border-error-500' : ''
                            }`}
                          />
                          <button
                            type="button"
                            onClick={() => setShowNewPassword(!showNewPassword)}
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          >
                            {showNewPassword ? (
                              <EyeSlashIcon className="h-4 w-4 text-text-tertiary" />
                            ) : (
                              <EyeIcon className="h-4 w-4 text-text-tertiary" />
                            )}
                          </button>
                        </div>
                        {passwordErrors.new_password && (
                          <p className="text-error-600 text-sm mt-1">{passwordErrors.new_password.message}</p>
                        )}
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Confirm New Password *
                        </label>
                        <div className="relative">
                          <input
                            type={showConfirmPassword ? 'text' : 'password'}
                            {...registerPassword('confirm_password')}
                            className={`input-primary w-full pr-10 ${
                              passwordErrors.confirm_password ? 'border-error-500 focus:border-error-500' : ''
                            }`}
                          />
                          <button
                            type="button"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          >
                            {showConfirmPassword ? (
                              <EyeSlashIcon className="h-4 w-4 text-text-tertiary" />
                            ) : (
                              <EyeIcon className="h-4 w-4 text-text-tertiary" />
                            )}
                          </button>
                        </div>
                        {passwordErrors.confirm_password && (
                          <p className="text-error-600 text-sm mt-1">{passwordErrors.confirm_password.message}</p>
                        )}
                      </div>
                      
                      <div className="flex justify-end">
                        <button
                          type="submit"
                          disabled={isPasswordSubmitting || updatePasswordMutation.isPending}
                          className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isPasswordSubmitting || updatePasswordMutation.isPending ? (
                            <>
                              <LoadingSpinner size="sm" className="mr-2" />
                              Updating...
                            </>
                          ) : (
                            'Update Password'
                          )}
                        </button>
                      </div>
                    </form>
                  </div>

                  {/* Delete Account */}
                  <div className="card border-error-200 dark:border-error-800">
                    <h2 className="text-lg font-semibold text-error-600 mb-4 flex items-center">
                      <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
                      Danger Zone
                    </h2>
                    
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-text-primary mb-2">Delete Account</h3>
                        <p className="text-sm text-text-secondary mb-4">
                          Once you delete your account, there is no going back. Please be certain.
                        </p>
                        <button
                          onClick={() => setShowDeleteModal(true)}
                          className="btn-danger flex items-center"
                        >
                          <TrashIcon className="h-4 w-4 mr-2" />
                          Delete Account
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'notifications' && (
                <div className="card">
                  <h2 className="text-lg font-semibold text-text-primary mb-6 flex items-center">
                    <BellIcon className="h-5 w-5 mr-2" />
                    Notification Preferences
                  </h2>
                  
                  <form onSubmit={handleNotificationSubmit(onNotificationSubmit)} className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-sm font-medium text-text-primary">Email Notifications</h3>
                          <p className="text-sm text-text-secondary">Receive notifications via email</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            {...registerNotification('email_notifications')}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-sm font-medium text-text-primary">Push Notifications</h3>
                          <p className="text-sm text-text-secondary">Receive push notifications in your browser</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            {...registerNotification('push_notifications')}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-sm font-medium text-text-primary">Subscription Reminders</h3>
                          <p className="text-sm text-text-secondary">Get notified before subscriptions renew</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            {...registerNotification('subscription_reminders')}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-sm font-medium text-text-primary">Payment Alerts</h3>
                          <p className="text-sm text-text-secondary">Get notified about payment activities</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            {...registerNotification('payment_alerts')}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-sm font-medium text-text-primary">Weekly Reports</h3>
                          <p className="text-sm text-text-secondary">Receive weekly spending summaries</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            {...registerNotification('weekly_reports')}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-sm font-medium text-text-primary">Marketing Emails</h3>
                          <p className="text-sm text-text-secondary">Receive updates about new features and tips</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            {...registerNotification('marketing_emails')}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                      </div>
                    </div>
                    
                    <div className="flex justify-end">
                      <button
                        type="submit"
                        disabled={isNotificationSubmitting || updateNotificationMutation.isPending || !isNotificationDirty}
                        className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isNotificationSubmitting || updateNotificationMutation.isPending ? (
                          <>
                            <LoadingSpinner size="sm" className="mr-2" />
                            Updating...
                          </>
                        ) : (
                          'Save Preferences'
                        )}
                      </button>
                    </div>
                  </form>
                </div>
              )}

              {activeTab === 'appearance' && (
                <div className="card">
                  <h2 className="text-lg font-semibold text-text-primary mb-6 flex items-center">
                    <PaintBrushIcon className="h-5 w-5 mr-2" />
                    Appearance Settings
                  </h2>
                  
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-sm font-medium text-text-primary mb-4">Theme</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                        {themeOptions.map((option) => {
                          const Icon = option.icon;
                          return (
                            <button
                              key={option.value}
                              onClick={() => setTheme(option.value as any)}
                              className={`p-4 border rounded-lg text-left transition-colors ${
                                theme === option.value
                                  ? 'border-primary-500 bg-primary-50 dark:bg-primary-900'
                                  : 'border-border-primary hover:border-border-secondary'
                              }`}
                            >
                              <div className="flex items-center space-x-3">
                                <Icon className={`h-5 w-5 ${
                                  theme === option.value ? 'text-primary-600' : 'text-text-tertiary'
                                }`} />
                                <div>
                                  <div className={`text-sm font-medium ${
                                    theme === option.value ? 'text-primary-700 dark:text-primary-300' : 'text-text-primary'
                                  }`}>
                                    {option.label}
                                  </div>
                                  {theme === option.value && (
                                    <div className="flex items-center mt-1">
                                      <CheckCircleIcon className="h-4 w-4 text-primary-600 mr-1" />
                                      <span className="text-xs text-primary-600">Active</span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </button>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'preferences' && (
                <div className="card">
                  <h2 className="text-lg font-semibold text-text-primary mb-6 flex items-center">
                    <CogIcon className="h-5 w-5 mr-2" />
                    General Preferences
                  </h2>
                  
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-sm font-medium text-text-primary mb-2">Date Format</h3>
                        <select className="input-primary w-full">
                          <option value="MM/DD/YYYY">MM/DD/YYYY (US)</option>
                          <option value="DD/MM/YYYY">DD/MM/YYYY (EU)</option>
                          <option value="YYYY-MM-DD">YYYY-MM-DD (ISO)</option>
                        </select>
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-text-primary mb-2">Number Format</h3>
                        <select className="input-primary w-full">
                          <option value="1,234.56">1,234.56 (US)</option>
                          <option value="1.234,56">1.234,56 (EU)</option>
                          <option value="1 234,56">1 234,56 (FR)</option>
                        </select>
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-text-primary mb-2">Start of Week</h3>
                        <select className="input-primary w-full">
                          <option value="sunday">Sunday</option>
                          <option value="monday">Monday</option>
                        </select>
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-text-primary mb-2">Fiscal Year Start</h3>
                        <select className="input-primary w-full">
                          <option value="january">January</option>
                          <option value="april">April</option>
                          <option value="july">July</option>
                          <option value="october">October</option>
                        </select>
                      </div>
                    </div>
                    
                    <div className="flex justify-end">
                      <button className="btn-primary">
                        Save Preferences
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Delete Account Modal */}
        <ConfirmationModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={() => deleteAccountMutation.mutate()}
          title="Delete Account"
          message="Are you absolutely sure you want to delete your account? This action cannot be undone and will permanently delete all your data including subscriptions, payments, and reimbursements."
          confirmText="Delete Account"
          confirmVariant="danger"
          isLoading={deleteAccountMutation.isPending}
        />
      </Layout>
    </>
  );
}