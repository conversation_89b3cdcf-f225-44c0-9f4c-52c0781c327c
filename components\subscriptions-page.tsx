"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Plus, Edit, Trash2, Calendar, DollarSign, AlertCircle, CheckCircle } from "lucide-react"
import { format, addDays, differenceInDays } from "date-fns"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { useCurrencyRates } from "@/hooks/useCurrencyRates"
import { cn } from "@/lib/utils"
import { SharedLayout } from "@/components/shared-layout"
import { Subscription, SUBSCRIPTION_CATEGORIES } from "@/types"

const categories = SUBSCRIPTION_CATEGORIES

export function SubscriptionsPage() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingSubscription, setEditingSubscription] = useState<Subscription | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterCategory, setFilterCategory] = useState("all")
  const [filterStatus, setFilterStatus] = useState("all")
  const { rates } = useCurrencyRates()

  useEffect(() => {
    const storedSubscriptions = JSON.parse(localStorage.getItem("subscriptions") || "[]")
    setSubscriptions(storedSubscriptions)
  }, [])

  const saveSubscriptions = (newSubscriptions: Subscription[]) => {
    localStorage.setItem("subscriptions", JSON.stringify(newSubscriptions))
    setSubscriptions(newSubscriptions)
  }

  const filteredSubscriptions = subscriptions.filter((sub) => {
    const matchesSearch =
      sub.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sub.category.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = filterCategory === "all" || sub.category === filterCategory
    const matchesStatus = filterStatus === "all" || sub.status === filterStatus
    return matchesSearch && matchesCategory && matchesStatus
  })

  const totalActiveSubscriptions = subscriptions.filter((s) => s.status === "active").length
  const totalMonthlyKwd = subscriptions
    .filter((s) => s.status === "active")
    .reduce((sum, s) => {
      const monthlyAmount =
        s.billingCycle === "yearly" ? s.kwdAmount / 12 : s.billingCycle === "quarterly" ? s.kwdAmount / 3 : s.kwdAmount
      return sum + monthlyAmount
    }, 0)

  const upcomingPayments = subscriptions
    .filter((s) => s.status === "active")
    .filter((s) => differenceInDays(new Date(s.nextPaymentDate), new Date()) <= 7).length

  const getDaysUntilPayment = (date: string) => {
    return differenceInDays(new Date(date), new Date())
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>
      case "cancelled":
        return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>
      case "paused":
        return <Badge className="bg-yellow-100 text-yellow-800">Paused</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getPaymentUrgency = (daysUntil: number) => {
    if (daysUntil < 0) return { color: "text-red-600", icon: AlertCircle, text: "Overdue" }
    if (daysUntil <= 3) return { color: "text-orange-600", icon: AlertCircle, text: `${daysUntil} days` }
    if (daysUntil <= 7) return { color: "text-yellow-600", icon: Calendar, text: `${daysUntil} days` }
    return { color: "text-green-600", icon: CheckCircle, text: `${daysUntil} days` }
  }

  return (
    <SharedLayout activeUrl="/subscriptions">
      <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Subscriptions</h1>
            <p className="text-muted-foreground">Manage your subscription services</p>
          </div>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => setEditingSubscription(null)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Subscription
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>{editingSubscription ? "Edit Subscription" : "Add New Subscription"}</DialogTitle>
                <DialogDescription>
                  {editingSubscription ? "Update subscription details" : "Add a new subscription service"}
                </DialogDescription>
              </DialogHeader>
              <SubscriptionForm
                subscription={editingSubscription}
                onSave={(subscription) => {
                  if (editingSubscription) {
                    const updated = subscriptions.map((s) => (s.id === editingSubscription.id ? subscription : s))
                    saveSubscriptions(updated)
                  } else {
                    saveSubscriptions([subscription, ...subscriptions])
                  }
                  setIsDialogOpen(false)
                  setEditingSubscription(null)
                }}
                onCancel={() => {
                  setIsDialogOpen(false)
                  setEditingSubscription(null)
                }}
              />
            </DialogContent>
          </Dialog>
        </div>

        {/* Keep all the rest of the existing content exactly the same */}
        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalActiveSubscriptions}</div>
              <p className="text-xs text-muted-foreground">Currently active</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Cost (KWD)</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalMonthlyKwd.toFixed(2)} KWD</div>
              <p className="text-xs text-muted-foreground">Estimated monthly</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Upcoming Payments</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{upcomingPayments}</div>
              <p className="text-xs text-muted-foreground">Next 7 days</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <div className="space-y-2">
                <Label htmlFor="search">Search</Label>
                <Input
                  id="search"
                  placeholder="Search subscriptions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select value={filterCategory} onValueChange={setFilterCategory}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="paused">Paused</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("")
                    setFilterCategory("all")
                    setFilterStatus("all")
                  }}
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Subscriptions List */}
        <Card>
          <CardHeader>
            <CardTitle>Your Subscriptions</CardTitle>
            <CardDescription>Manage all your subscription services</CardDescription>
          </CardHeader>
          <CardContent>
            {filteredSubscriptions.length === 0 ? (
              <div className="text-center py-8">
                <DollarSign className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-4 text-lg font-medium">No subscriptions found</h3>
                <p className="text-muted-foreground">
                  {subscriptions.length === 0 ? "Add your first subscription" : "Try adjusting your filters"}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredSubscriptions.map((subscription) => {
                  const daysUntil = getDaysUntilPayment(subscription.nextPaymentDate)
                  const urgency = getPaymentUrgency(daysUntil)
                  const UrgencyIcon = urgency.icon

                  return (
                    <div
                      key={subscription.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <DollarSign className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{subscription.name}</h4>
                            {getStatusBadge(subscription.status)}
                          </div>
                          <p className="text-sm text-muted-foreground">{subscription.category}</p>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                            <span>
                              <span className="font-semibold">{subscription.kwdAmount.toFixed(2)} KWD</span> (
                              {subscription.amount.toFixed(2)} {subscription.currency})
                            </span>
                            <span>•</span>
                            <span className="capitalize">{subscription.billingCycle}</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        {subscription.status === "active" && (
                          <div className={`flex items-center space-x-1 ${urgency.color}`}>
                            <UrgencyIcon className="h-4 w-4" />
                            <span className="text-sm font-medium">
                              {daysUntil < 0 ? urgency.text : `${urgency.text} left`}
                            </span>
                          </div>
                        )}

                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setEditingSubscription(subscription)
                              setIsDialogOpen(true)
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Subscription</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete "{subscription.name}"? This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => {
                                    const updated = subscriptions.filter((s) => s.id !== subscription.id)
                                    saveSubscriptions(updated)
                                  }}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </SharedLayout>
  )
}

function SubscriptionForm({
  subscription,
  onSave,
  onCancel,
}: {
  subscription: Subscription | null
  onSave: (subscription: Subscription) => void
  onCancel: () => void
}) {
  const { rates } = useCurrencyRates()
  const [formData, setFormData] = useState({
    name: subscription?.name || "",
    category: subscription?.category || "",
    amount: subscription?.amount.toString() || "",
    currency: subscription?.currency || ("USD" as "USD" | "GBP"),
    billingCycle: subscription?.billingCycle || ("monthly" as "monthly" | "yearly" | "quarterly"),
    nextPaymentDate: subscription ? new Date(subscription.nextPaymentDate) : addDays(new Date(), 30),
    status: subscription?.status || ("active" as "active" | "cancelled" | "paused"),
    description: subscription?.description || "",
  })

  const kwdAmount =
    Number.parseFloat(formData.amount) * (formData.currency === "USD" ? rates.usdToKwd : rates.gbpToKwd) || 0

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    const subscriptionData: Subscription = {
      id: subscription?.id || `sub_${Date.now()}`,
      name: formData.name,
      category: formData.category,
      amount: Number.parseFloat(formData.amount),
      currency: formData.currency,
      kwdAmount,
      exchangeRate: formData.currency === "USD" ? rates.usdToKwd : rates.gbpToKwd,
      billingCycle: formData.billingCycle,
      nextPaymentDate: formData.nextPaymentDate.toISOString(),
      status: formData.status,
      description: formData.description,
      createdAt: subscription?.createdAt || new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
    }

    onSave(subscriptionData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Subscription Name</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="category">Category</Label>
          <Select
            value={formData.category}
            onValueChange={(value) => setFormData((prev) => ({ ...prev, category: value }))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="amount">Amount</Label>
          <Input
            id="amount"
            type="number"
            step="0.01"
            value={formData.amount}
            onChange={(e) => setFormData((prev) => ({ ...prev, amount: e.target.value }))}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="currency">Currency</Label>
          <Select
            value={formData.currency}
            onValueChange={(value: "USD" | "GBP") => setFormData((prev) => ({ ...prev, currency: value }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="USD">USD ($)</SelectItem>
              <SelectItem value="GBP">GBP (£)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="billing">Billing Cycle</Label>
          <Select
            value={formData.billingCycle}
            onValueChange={(value: "monthly" | "yearly" | "quarterly") =>
              setFormData((prev) => ({ ...prev, billingCycle: value }))
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {formData.amount && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="pt-4">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-700">
                {kwdAmount.toFixed(2)} KWD per {formData.billingCycle.replace("ly", "")}
              </div>
              <div className="text-sm text-muted-foreground">
                Exchange rate: 1 {formData.currency} ={" "}
                {(formData.currency === "USD" ? rates.usdToKwd : rates.gbpToKwd).toFixed(3)} KWD
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Next Payment Date</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !formData.nextPaymentDate && "text-muted-foreground",
                )}
              >
                <Calendar className="mr-2 h-4 w-4" />
                {formData.nextPaymentDate ? format(formData.nextPaymentDate, "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <CalendarComponent
                mode="single"
                selected={formData.nextPaymentDate}
                onSelect={(date) => date && setFormData((prev) => ({ ...prev, nextPaymentDate: date }))}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select
            value={formData.status}
            onValueChange={(value: "active" | "cancelled" | "paused") =>
              setFormData((prev) => ({ ...prev, status: value }))
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="paused">Paused</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description (Optional)</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
          rows={3}
        />
      </div>

      <div className="flex gap-4">
        <Button type="submit" className="flex-1">
          {subscription ? "Update Subscription" : "Add Subscription"}
        </Button>
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
      </div>
    </form>
  )
}
