const cron = require('node-cron');
const { createClient } = require('@supabase/supabase-js');
const logger = require('./logger');

// Initialize Supabase client for cron jobs
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

/**
 * Fetch exchange rates from external API
 * Using a free API service for demonstration
 */
async function fetchExchangeRates() {
  try {
    logger.info('Starting exchange rate update job');
    
    const baseCurrency = 'KWD';
    const targetCurrencies = ['USD', 'EUR', 'GBP', 'SAR', 'AED', 'QAR', 'BHD', 'OMR', 'JOD'];
    const today = new Date().toISOString().split('T')[0];
    
    // Check if rates for today already exist
    const { data: existingRates } = await supabase
      .from('exchange_rates')
      .select('base_currency, target_currency')
      .eq('date', today)
      .eq('base_currency', baseCurrency);
    
    const existingPairs = new Set(
      existingRates?.map(rate => `${rate.base_currency}-${rate.target_currency}`) || []
    );
    
    // Mock exchange rates (in production, you would fetch from a real API)
    // For demonstration, using approximate rates
    const mockRates = {
      'KWD-USD': 3.25,
      'KWD-EUR': 2.98,
      'KWD-GBP': 2.56,
      'KWD-SAR': 12.19,
      'KWD-AED': 11.94,
      'KWD-QAR': 11.83,
      'KWD-BHD': 1.22,
      'KWD-OMR': 1.25,
      'KWD-JOD': 2.30
    };
    
    const ratesToInsert = [];
    
    // Add base currency to itself
    if (!existingPairs.has(`${baseCurrency}-${baseCurrency}`)) {
      ratesToInsert.push({
        base_currency: baseCurrency,
        target_currency: baseCurrency,
        rate: 1.0,
        date: today,
        source: 'system'
      });
    }
    
    // Add rates for target currencies
    for (const targetCurrency of targetCurrencies) {
      const pairKey = `${baseCurrency}-${targetCurrency}`;
      
      if (!existingPairs.has(pairKey)) {
        const rate = mockRates[pairKey];
        if (rate) {
          ratesToInsert.push({
            base_currency: baseCurrency,
            target_currency: targetCurrency,
            rate: rate,
            date: today,
            source: 'mock_api'
          });
          
          // Also add reverse rate
          const reversePairKey = `${targetCurrency}-${baseCurrency}`;
          if (!existingPairs.has(reversePairKey)) {
            ratesToInsert.push({
              base_currency: targetCurrency,
              target_currency: baseCurrency,
              rate: 1 / rate,
              date: today,
              source: 'mock_api'
            });
          }
        }
      }
    }
    
    // Add cross-currency rates (USD as intermediate)
    const usdRate = mockRates['KWD-USD'];
    if (usdRate) {
      for (let i = 0; i < targetCurrencies.length; i++) {
        for (let j = i + 1; j < targetCurrencies.length; j++) {
          const fromCurrency = targetCurrencies[i];
          const toCurrency = targetCurrencies[j];
          
          const fromToUsdRate = 1 / mockRates[`KWD-${fromCurrency}`];
          const toToUsdRate = 1 / mockRates[`KWD-${toCurrency}`];
          
          if (fromToUsdRate && toToUsdRate) {
            const crossRate = toToUsdRate / fromToUsdRate;
            
            const pairKey = `${fromCurrency}-${toCurrency}`;
            const reversePairKey = `${toCurrency}-${fromCurrency}`;
            
            if (!existingPairs.has(pairKey)) {
              ratesToInsert.push({
                base_currency: fromCurrency,
                target_currency: toCurrency,
                rate: crossRate,
                date: today,
                source: 'calculated'
              });
            }
            
            if (!existingPairs.has(reversePairKey)) {
              ratesToInsert.push({
                base_currency: toCurrency,
                target_currency: fromCurrency,
                rate: 1 / crossRate,
                date: today,
                source: 'calculated'
              });
            }
          }
        }
      }
    }
    
    if (ratesToInsert.length > 0) {
      const { error } = await supabase
        .from('exchange_rates')
        .insert(ratesToInsert);
      
      if (error) {
        logger.error('Failed to insert exchange rates', { error: error.message });
        throw error;
      }
      
      logger.info('Exchange rates updated successfully', {
        ratesInserted: ratesToInsert.length,
        date: today
      });
    } else {
      logger.info('Exchange rates already up to date', { date: today });
    }
    
  } catch (error) {
    logger.error('Exchange rate update job failed', {
      error: error.message,
      stack: error.stack
    });
  }
}

/**
 * Check for upcoming subscription renewals and send notifications
 */
async function checkUpcomingRenewals() {
  try {
    logger.info('Starting upcoming renewals check job');
    
    const today = new Date();
    const threeDaysFromNow = new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000);
    const sevenDaysFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    const todayStr = today.toISOString().split('T')[0];
    const threeDaysStr = threeDaysFromNow.toISOString().split('T')[0];
    const sevenDaysStr = sevenDaysFromNow.toISOString().split('T')[0];
    
    // Get subscriptions with upcoming renewals
    const { data: upcomingRenewals, error } = await supabase
      .from('subscriptions')
      .select(`
        id,
        name,
        amount,
        currency,
        billing_cycle,
        next_billing_date,
        businesses!inner(
          id,
          name,
          owner_id,
          profiles!inner(
            id,
            email,
            full_name
          )
        )
      `)
      .eq('status', 'active')
      .or(`next_billing_date.eq.${todayStr},next_billing_date.eq.${threeDaysStr},next_billing_date.eq.${sevenDaysStr}`);
    
    if (error) {
      logger.error('Failed to fetch upcoming renewals', { error: error.message });
      throw error;
    }
    
    if (!upcomingRenewals || upcomingRenewals.length === 0) {
      logger.info('No upcoming renewals found');
      return;
    }
    
    // Group renewals by user and urgency
    const renewalsByUser = {};
    
    upcomingRenewals.forEach(subscription => {
      const userId = subscription.businesses.owner_id;
      const userEmail = subscription.businesses.profiles.email;
      const userName = subscription.businesses.profiles.full_name;
      
      if (!renewalsByUser[userId]) {
        renewalsByUser[userId] = {
          email: userEmail,
          name: userName,
          today: [],
          threeDays: [],
          sevenDays: []
        };
      }
      
      if (subscription.next_billing_date === todayStr) {
        renewalsByUser[userId].today.push(subscription);
      } else if (subscription.next_billing_date === threeDaysStr) {
        renewalsByUser[userId].threeDays.push(subscription);
      } else if (subscription.next_billing_date === sevenDaysStr) {
        renewalsByUser[userId].sevenDays.push(subscription);
      }
    });
    
    // In a real application, you would send emails here
    // For now, we'll just log the notifications
    for (const [userId, userRenewals] of Object.entries(renewalsByUser)) {
      if (userRenewals.today.length > 0) {
        logger.info('Subscription renewal due today', {
          userId,
          userEmail: userRenewals.email,
          subscriptions: userRenewals.today.map(s => ({ id: s.id, name: s.name, amount: s.amount }))
        });
      }
      
      if (userRenewals.threeDays.length > 0) {
        logger.info('Subscription renewal due in 3 days', {
          userId,
          userEmail: userRenewals.email,
          subscriptions: userRenewals.threeDays.map(s => ({ id: s.id, name: s.name, amount: s.amount }))
        });
      }
      
      if (userRenewals.sevenDays.length > 0) {
        logger.info('Subscription renewal due in 7 days', {
          userId,
          userEmail: userRenewals.email,
          subscriptions: userRenewals.sevenDays.map(s => ({ id: s.id, name: s.name, amount: s.amount }))
        });
      }
    }
    
    logger.info('Upcoming renewals check completed', {
      totalRenewals: upcomingRenewals.length,
      usersNotified: Object.keys(renewalsByUser).length
    });
    
  } catch (error) {
    logger.error('Upcoming renewals check job failed', {
      error: error.message,
      stack: error.stack
    });
  }
}

/**
 * Update next billing dates for subscriptions
 */
async function updateNextBillingDates() {
  try {
    logger.info('Starting next billing dates update job');
    
    const today = new Date().toISOString().split('T')[0];
    
    // Get subscriptions that need billing date updates
    const { data: subscriptions, error } = await supabase
      .from('subscriptions')
      .select('id, billing_cycle, next_billing_date')
      .eq('status', 'active')
      .lte('next_billing_date', today);
    
    if (error) {
      logger.error('Failed to fetch subscriptions for billing update', { error: error.message });
      throw error;
    }
    
    if (!subscriptions || subscriptions.length === 0) {
      logger.info('No subscriptions need billing date updates');
      return;
    }
    
    const updates = [];
    
    subscriptions.forEach(subscription => {
      const currentDate = new Date(subscription.next_billing_date);
      let nextDate;
      
      switch (subscription.billing_cycle) {
        case 'weekly':
          nextDate = new Date(currentDate.getTime() + 7 * 24 * 60 * 60 * 1000);
          break;
        case 'monthly':
          nextDate = new Date(currentDate);
          nextDate.setMonth(nextDate.getMonth() + 1);
          break;
        case 'quarterly':
          nextDate = new Date(currentDate);
          nextDate.setMonth(nextDate.getMonth() + 3);
          break;
        case 'yearly':
          nextDate = new Date(currentDate);
          nextDate.setFullYear(nextDate.getFullYear() + 1);
          break;
        default:
          // Default to monthly if billing cycle is unknown
          nextDate = new Date(currentDate);
          nextDate.setMonth(nextDate.getMonth() + 1);
      }
      
      updates.push({
        id: subscription.id,
        next_billing_date: nextDate.toISOString().split('T')[0]
      });
    });
    
    // Update billing dates in batches
    const batchSize = 100;
    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);
      
      for (const update of batch) {
        const { error: updateError } = await supabase
          .from('subscriptions')
          .update({ next_billing_date: update.next_billing_date })
          .eq('id', update.id);
        
        if (updateError) {
          logger.error('Failed to update subscription billing date', {
            subscriptionId: update.id,
            error: updateError.message
          });
        }
      }
    }
    
    logger.info('Next billing dates updated successfully', {
      subscriptionsUpdated: updates.length
    });
    
  } catch (error) {
    logger.error('Next billing dates update job failed', {
      error: error.message,
      stack: error.stack
    });
  }
}

/**
 * Clean up old exchange rates (keep only last 90 days)
 */
async function cleanupOldExchangeRates() {
  try {
    logger.info('Starting exchange rates cleanup job');
    
    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
    const cutoffDate = ninetyDaysAgo.toISOString().split('T')[0];
    
    const { error } = await supabase
      .from('exchange_rates')
      .delete()
      .lt('date', cutoffDate);
    
    if (error) {
      logger.error('Failed to cleanup old exchange rates', { error: error.message });
      throw error;
    }
    
    logger.info('Old exchange rates cleaned up successfully', {
      cutoffDate
    });
    
  } catch (error) {
    logger.error('Exchange rates cleanup job failed', {
      error: error.message,
      stack: error.stack
    });
  }
}

/**
 * Initialize and start all cron jobs
 */
function initializeCronJobs() {
  logger.info('Initializing cron jobs');
  
  // Update exchange rates daily at 2:00 AM
  cron.schedule('0 2 * * *', fetchExchangeRates, {
    scheduled: true,
    timezone: 'Asia/Kuwait'
  });
  
  // Check upcoming renewals daily at 9:00 AM
  cron.schedule('0 9 * * *', checkUpcomingRenewals, {
    scheduled: true,
    timezone: 'Asia/Kuwait'
  });
  
  // Update next billing dates daily at 1:00 AM
  cron.schedule('0 1 * * *', updateNextBillingDates, {
    scheduled: true,
    timezone: 'Asia/Kuwait'
  });
  
  // Cleanup old exchange rates weekly on Sunday at 3:00 AM
  cron.schedule('0 3 * * 0', cleanupOldExchangeRates, {
    scheduled: true,
    timezone: 'Asia/Kuwait'
  });
  
  logger.info('Cron jobs initialized successfully');
  
  // Run initial exchange rate fetch if no rates exist for today
  setTimeout(async () => {
    const today = new Date().toISOString().split('T')[0];
    const { data: existingRates } = await supabase
      .from('exchange_rates')
      .select('id')
      .eq('date', today)
      .limit(1);
    
    if (!existingRates || existingRates.length === 0) {
      logger.info('No exchange rates found for today, running initial fetch');
      await fetchExchangeRates();
    }
  }, 5000); // Wait 5 seconds after server start
}

/**
 * Stop all cron jobs
 */
function stopCronJobs() {
  logger.info('Stopping all cron jobs');
  cron.getTasks().forEach(task => {
    task.stop();
  });
}

/**
 * Manual trigger functions for testing
 */
const manualTriggers = {
  fetchExchangeRates,
  checkUpcomingRenewals,
  updateNextBillingDates,
  cleanupOldExchangeRates
};

module.exports = {
  initializeCronJobs,
  stopCronJobs,
  manualTriggers
};