import { useState, useMemo } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import ConfirmationModal from '../../components/ui/ConfirmationModal';
import { api } from '../../lib/api';
import { formatCurrency, formatDate, formatRelativeTime } from '../../lib/utils';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowsUpDownIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CreditCardIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';
import {
  CheckCircleIcon as CheckCircleIconSolid,
  ClockIcon as ClockIconSolid,
  XCircleIcon as XCircleIconSolid,
} from '@heroicons/react/24/solid';

// Payment status configurations
const statusConfig = {
  completed: {
    label: 'Completed',
    icon: CheckCircleIconSolid,
    className: 'text-success-600 bg-success-100 dark:bg-success-900',
  },
  pending: {
    label: 'Pending',
    icon: ClockIconSolid,
    className: 'text-warning-600 bg-warning-100 dark:bg-warning-900',
  },
  failed: {
    label: 'Failed',
    icon: XCircleIconSolid,
    className: 'text-error-600 bg-error-100 dark:bg-error-900',
  },
};

// Payment method configurations
const paymentMethodConfig = {
  credit_card: { label: 'Credit Card', icon: CreditCardIcon },
  bank_transfer: { label: 'Bank Transfer', icon: BuildingOfficeIcon },
  paypal: { label: 'PayPal', icon: DocumentTextIcon },
  other: { label: 'Other', icon: DocumentTextIcon },
};

export default function PaymentsPage() {
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [businessFilter, setBusinessFilter] = useState('all');
  const [subscriptionFilter, setSubscriptionFilter] = useState('all');
  const [sortBy, setSortBy] = useState('payment_date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [deletePaymentId, setDeletePaymentId] = useState<string | null>(null);

  // Fetch payments
  const { data: payments = [], isLoading } = useQuery({
    queryKey: ['payments'],
    queryFn: () => api.payments.getAll(),
  });

  // Fetch businesses for filter
  const { data: businesses = [] } = useQuery({
    queryKey: ['businesses'],
    queryFn: () => api.businesses.getAll(),
  });

  // Fetch subscriptions for filter
  const { data: subscriptions = [] } = useQuery({
    queryKey: ['subscriptions'],
    queryFn: () => api.subscriptions.getAll(),
  });

  // Delete payment mutation
  const deleteMutation = useMutation({
    mutationFn: (id: string) => api.payments.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payments'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Payment deleted successfully!');
      setDeletePaymentId(null);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete payment');
    },
  });

  // Filter and sort payments
  const filteredAndSortedPayments = useMemo(() => {
    let filtered = payments.filter((payment: any) => {
      const matchesSearch = 
        payment.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payment.subscription?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payment.business?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payment.amount.toString().includes(searchTerm);
      
      const matchesStatus = statusFilter === 'all' || payment.status === statusFilter;
      const matchesBusiness = businessFilter === 'all' || payment.business_id === businessFilter;
      const matchesSubscription = subscriptionFilter === 'all' || payment.subscription_id === subscriptionFilter;
      
      return matchesSearch && matchesStatus && matchesBusiness && matchesSubscription;
    });

    // Sort payments
    filtered.sort((a: any, b: any) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];
      
      // Handle nested properties
      if (sortBy === 'business_name') {
        aValue = a.business?.name || '';
        bValue = b.business?.name || '';
      } else if (sortBy === 'subscription_name') {
        aValue = a.subscription?.name || '';
        bValue = b.subscription?.name || '';
      }
      
      // Handle different data types
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [payments, searchTerm, statusFilter, businessFilter, subscriptionFilter, sortBy, sortOrder]);

  // Calculate summary statistics
  const stats = useMemo(() => {
    const total = payments.length;
    const completed = payments.filter((p: any) => p.status === 'completed').length;
    const pending = payments.filter((p: any) => p.status === 'pending').length;
    const failed = payments.filter((p: any) => p.status === 'failed').length;
    const totalAmount = payments
      .filter((p: any) => p.status === 'completed')
      .reduce((sum: number, p: any) => sum + p.amount, 0);
    
    return { total, completed, pending, failed, totalAmount };
  }, [payments]);

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const getBusinessName = (businessId: string) => {
    const business = businesses.find((b: any) => b.id === businessId);
    return business?.name || 'Unknown Business';
  };

  const getSubscriptionName = (subscriptionId: string) => {
    const subscription = subscriptions.find((s: any) => s.id === subscriptionId);
    return subscription?.name || 'Unknown Subscription';
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading payments..." />
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Payments - SubDash</title>
        <meta name="description" content="Manage your subscription payments" />
      </Head>

      <Layout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-text-primary">Payments</h1>
              <p className="text-sm text-text-secondary mt-1">
                Track and manage all your subscription payments
              </p>
            </div>
            <Link href="/payments/new" className="btn-primary flex items-center">
              <PlusIcon className="h-4 w-4 mr-2" />
              Record Payment
            </Link>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="card">
              <div className="flex items-center">
                <div className="p-2 bg-primary-100 dark:bg-primary-900 rounded-lg">
                  <CreditCardIcon className="h-5 w-5 text-primary-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-text-secondary">Total Payments</p>
                  <p className="text-lg font-semibold text-text-primary">{stats.total}</p>
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="flex items-center">
                <div className="p-2 bg-success-100 dark:bg-success-900 rounded-lg">
                  <CheckCircleIcon className="h-5 w-5 text-success-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-text-secondary">Completed</p>
                  <p className="text-lg font-semibold text-text-primary">{stats.completed}</p>
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="flex items-center">
                <div className="p-2 bg-warning-100 dark:bg-warning-900 rounded-lg">
                  <ClockIcon className="h-5 w-5 text-warning-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-text-secondary">Pending</p>
                  <p className="text-lg font-semibold text-text-primary">{stats.pending}</p>
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="flex items-center">
                <div className="p-2 bg-error-100 dark:bg-error-900 rounded-lg">
                  <XCircleIcon className="h-5 w-5 text-error-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-text-secondary">Failed</p>
                  <p className="text-lg font-semibold text-text-primary">{stats.failed}</p>
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="flex items-center">
                <div className="p-2 bg-primary-100 dark:bg-primary-900 rounded-lg">
                  <CurrencyDollarIcon className="h-5 w-5 text-primary-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-text-secondary">Total Amount</p>
                  <p className="text-lg font-semibold text-text-primary">
                    {formatCurrency(stats.totalAmount, 'USD')}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="card">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary" />
                  <input
                    type="text"
                    placeholder="Search payments..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="input-primary pl-10 w-full"
                  />
                </div>
              </div>
              
              {/* Filters */}
              <div className="flex flex-col sm:flex-row gap-3">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="input-primary min-w-[120px]"
                >
                  <option value="all">All Status</option>
                  <option value="completed">Completed</option>
                  <option value="pending">Pending</option>
                  <option value="failed">Failed</option>
                </select>
                
                <select
                  value={businessFilter}
                  onChange={(e) => setBusinessFilter(e.target.value)}
                  className="input-primary min-w-[150px]"
                >
                  <option value="all">All Businesses</option>
                  {businesses.map((business: any) => (
                    <option key={business.id} value={business.id}>
                      {business.name}
                    </option>
                  ))}
                </select>
                
                <select
                  value={subscriptionFilter}
                  onChange={(e) => setSubscriptionFilter(e.target.value)}
                  className="input-primary min-w-[150px]"
                >
                  <option value="all">All Subscriptions</option>
                  {subscriptions.map((subscription: any) => (
                    <option key={subscription.id} value={subscription.id}>
                      {subscription.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Payments Table */}
          <div className="card overflow-hidden">
            {filteredAndSortedPayments.length === 0 ? (
              <div className="text-center py-12">
                <CreditCardIcon className="h-12 w-12 text-text-tertiary mx-auto mb-4" />
                <h3 className="text-lg font-medium text-text-primary mb-2">No payments found</h3>
                <p className="text-text-secondary mb-6">
                  {payments.length === 0 
                    ? "You haven't recorded any payments yet."
                    : "No payments match your current filters."
                  }
                </p>
                {payments.length === 0 && (
                  <Link href="/payments/new" className="btn-primary">
                    Record Your First Payment
                  </Link>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-border-primary">
                  <thead className="bg-background-secondary">
                    <tr>
                      <th 
                        className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-background-tertiary"
                        onClick={() => handleSort('payment_date')}
                      >
                        <div className="flex items-center">
                          Date
                          <ArrowsUpDownIcon className="h-4 w-4 ml-1" />
                        </div>
                      </th>
                      <th 
                        className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-background-tertiary"
                        onClick={() => handleSort('subscription_name')}
                      >
                        <div className="flex items-center">
                          Subscription
                          <ArrowsUpDownIcon className="h-4 w-4 ml-1" />
                        </div>
                      </th>
                      <th 
                        className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-background-tertiary"
                        onClick={() => handleSort('business_name')}
                      >
                        <div className="flex items-center">
                          Business
                          <ArrowsUpDownIcon className="h-4 w-4 ml-1" />
                        </div>
                      </th>
                      <th 
                        className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-background-tertiary"
                        onClick={() => handleSort('amount')}
                      >
                        <div className="flex items-center">
                          Amount
                          <ArrowsUpDownIcon className="h-4 w-4 ml-1" />
                        </div>
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Method
                      </th>
                      <th 
                        className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-background-tertiary"
                        onClick={() => handleSort('status')}
                      >
                        <div className="flex items-center">
                          Status
                          <ArrowsUpDownIcon className="h-4 w-4 ml-1" />
                        </div>
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-background-primary divide-y divide-border-primary">
                    {filteredAndSortedPayments.map((payment: any) => {
                      const status = payment.status as keyof typeof statusConfig;
                      const StatusIcon = statusConfig[status]?.icon || CheckCircleIconSolid;
                      const paymentMethod = payment.payment_method as keyof typeof paymentMethodConfig;
                      const MethodIcon = paymentMethodConfig[paymentMethod]?.icon || DocumentTextIcon;
                      
                      return (
                        <tr key={payment.id} className="hover:bg-background-secondary">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-text-primary">
                              {formatDate(payment.payment_date)}
                            </div>
                            <div className="text-xs text-text-secondary">
                              {formatRelativeTime(payment.payment_date)}
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="text-sm font-medium text-text-primary">
                              {getSubscriptionName(payment.subscription_id)}
                            </div>
                            {payment.description && (
                              <div className="text-xs text-text-secondary truncate max-w-[200px]">
                                {payment.description}
                              </div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-text-primary">
                              {getBusinessName(payment.business_id)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-text-primary">
                              {formatCurrency(payment.amount, payment.currency)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center text-sm text-text-primary">
                              <MethodIcon className="h-4 w-4 mr-2 text-text-tertiary" />
                              {paymentMethodConfig[paymentMethod]?.label || payment.payment_method}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              statusConfig[status]?.className || 'text-text-secondary bg-background-secondary'
                            }`}>
                              <StatusIcon className="h-3 w-3 mr-1" />
                              {statusConfig[status]?.label || payment.status}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex items-center justify-end space-x-2">
                              <Link
                                href={`/payments/${payment.id}`}
                                className="text-text-secondary hover:text-primary-600 transition-colors"
                                title="View details"
                              >
                                <EyeIcon className="h-4 w-4" />
                              </Link>
                              <Link
                                href={`/payments/${payment.id}/edit`}
                                className="text-text-secondary hover:text-primary-600 transition-colors"
                                title="Edit payment"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </Link>
                              <button
                                onClick={() => setDeletePaymentId(payment.id)}
                                className="text-text-secondary hover:text-error-600 transition-colors"
                                title="Delete payment"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Results Summary */}
          {filteredAndSortedPayments.length > 0 && (
            <div className="text-sm text-text-secondary text-center">
              Showing {filteredAndSortedPayments.length} of {payments.length} payments
            </div>
          )}
        </div>

        {/* Delete Confirmation Modal */}
        <ConfirmationModal
          isOpen={!!deletePaymentId}
          onClose={() => setDeletePaymentId(null)}
          onConfirm={() => deletePaymentId && deleteMutation.mutate(deletePaymentId)}
          title="Delete Payment"
          message="Are you sure you want to delete this payment? This action cannot be undone."
          confirmText="Delete"
          type="danger"
          loading={deleteMutation.isPending}
        />
      </Layout>
    </>
  );
}