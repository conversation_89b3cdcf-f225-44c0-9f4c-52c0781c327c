import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import ConfirmationModal from '../../components/ui/ConfirmationModal';
import { api } from '../../lib/api';
import { formatCurrency, formatDate, formatRelativeTime } from '../../lib/utils';
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  PlayIcon,
  PauseIcon,
  XMarkIcon,
  CreditCardIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  BuildingOfficeIcon,
  DocumentTextIcon,
  InformationCircleIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  GlobeAltIcon,
  TagIcon,
  BellIcon,
} from '@heroicons/react/24/outline';
import {
  CheckCircleIcon as CheckCircleIconSolid,
  PauseCircleIcon as PauseCircleIconSolid,
  XCircleIcon as XCircleIconSolid,
} from '@heroicons/react/24/solid';

// Validation schema
const subscriptionSchema = z.object({
  name: z.string().min(2, 'Subscription name must be at least 2 characters'),
  description: z.string().optional(),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  currency: z.string().min(3, 'Please select a currency'),
  billing_cycle: z.enum(['weekly', 'monthly', 'quarterly', 'yearly'], {
    required_error: 'Please select a billing cycle',
  }),
  billing_date: z.string().min(1, 'Please select a billing date'),
  business_id: z.string().min(1, 'Please select a business'),
  category: z.string().optional(),
  website: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  notes: z.string().optional(),
  auto_renew: z.boolean().default(true),
  reminder_days: z.number().min(0).max(30).default(3),
});

type SubscriptionFormData = z.infer<typeof subscriptionSchema>;

// Common currencies
const currencies = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'KWD', name: 'Kuwaiti Dinar', symbol: 'د.ك' },
  { code: 'SAR', name: 'Saudi Riyal', symbol: 'ر.س' },
  { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ' },
  { code: 'QAR', name: 'Qatari Riyal', symbol: 'ر.ق' },
  { code: 'BHD', name: 'Bahraini Dinar', symbol: '.د.ب' },
  { code: 'OMR', name: 'Omani Rial', symbol: 'ر.ع.' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
];

// Billing cycles
const billingCycles = [
  { value: 'weekly', label: 'Weekly', description: 'Billed every week' },
  { value: 'monthly', label: 'Monthly', description: 'Billed every month' },
  { value: 'quarterly', label: 'Quarterly', description: 'Billed every 3 months' },
  { value: 'yearly', label: 'Yearly', description: 'Billed every year' },
];

// Common subscription categories
const categories = [
  'Software & Tools',
  'Marketing & Advertising',
  'Communication',
  'Design & Creative',
  'Productivity',
  'Security',
  'Analytics',
  'Storage & Backup',
  'Entertainment',
  'Education',
  'Finance',
  'Other',
];

// Status configurations
const statusConfig = {
  active: {
    label: 'Active',
    icon: CheckCircleIconSolid,
    className: 'text-success-600 bg-success-100 dark:bg-success-900',
  },
  paused: {
    label: 'Paused',
    icon: PauseCircleIconSolid,
    className: 'text-warning-600 bg-warning-100 dark:bg-warning-900',
  },
  cancelled: {
    label: 'Cancelled',
    icon: XCircleIconSolid,
    className: 'text-error-600 bg-error-100 dark:bg-error-900',
  },
};

export default function SubscriptionDetailPage() {
  const router = useRouter();
  const { id } = router.query;
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showPauseModal, setShowPauseModal] = useState(false);
  const [showResumeModal, setShowResumeModal] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
  } = useForm<SubscriptionFormData>({
    resolver: zodResolver(subscriptionSchema),
  });

  // Watch form values
  const watchedAmount = watch('amount');
  const watchedCurrency = watch('currency');
  const watchedBillingCycle = watch('billing_cycle');

  // Fetch subscription details
  const { data: subscription, isLoading, error } = useQuery({
    queryKey: ['subscription', id],
    queryFn: async () => {
      const response = await api.subscriptions.getById(id as string);
      return response.data;
    },
    enabled: !!id,
  });

  // Fetch businesses
  const { data: businesses = [] } = useQuery({
    queryKey: ['businesses'],
    queryFn: async () => {
      const response = await api.businesses.getAll();
      return response.data || [];
    },
  });

  // Fetch recent payments for this subscription
  const { data: recentPayments = [] } = useQuery({
    queryKey: ['payments', 'subscription', id],
    queryFn: async () => {
      const response = await api.payments.getAll({ subscription_id: id as string, limit: 5 });
      return response.data || [];
    },
    enabled: !!id,
  });

  // Set form values when subscription data is loaded
  useEffect(() => {
    if (subscription) {
      reset({
        name: subscription.name,
        description: subscription.description || '',
        amount: subscription.amount,
        currency: subscription.currency,
        billing_cycle: subscription.billing_cycle,
        billing_date: subscription.billing_date?.split('T')[0] || '',
        business_id: subscription.business_id,
        category: subscription.category || '',
        website: subscription.website || '',
        notes: subscription.notes || '',
        auto_renew: subscription.auto_renew ?? true,
        reminder_days: subscription.reminder_days ?? 3,
      });
    }
  }, [subscription, reset]);

  // Update subscription mutation
  const updateMutation = useMutation({
    mutationFn: (data: SubscriptionFormData) => api.subscriptions.update(id as string, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription', id] });
      queryClient.invalidateQueries({ queryKey: ['subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Subscription updated successfully!');
      setIsEditing(false);
      setIsSubmitting(false);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update subscription');
      setIsSubmitting(false);
    },
  });

  // Delete subscription mutation
  const deleteMutation = useMutation({
    mutationFn: () => api.subscriptions.delete(id as string),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Subscription deleted successfully!');
      router.push('/subscriptions');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete subscription');
    },
  });

  // Pause subscription mutation
  const pauseMutation = useMutation({
    mutationFn: () => api.subscriptions.pause(id as string),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription', id] });
      queryClient.invalidateQueries({ queryKey: ['subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Subscription paused successfully!');
      setShowPauseModal(false);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to pause subscription');
    },
  });

  // Resume subscription mutation
  const resumeMutation = useMutation({
    mutationFn: () => api.subscriptions.resume(id as string),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription', id] });
      queryClient.invalidateQueries({ queryKey: ['subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Subscription resumed successfully!');
      setShowResumeModal(false);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to resume subscription');
    },
  });

  // Cancel subscription mutation
  const cancelMutation = useMutation({
    mutationFn: () => api.subscriptions.cancel(id as string),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription', id] });
      queryClient.invalidateQueries({ queryKey: ['subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Subscription cancelled successfully!');
      setShowCancelModal(false);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to cancel subscription');
    },
  });

  const onSubmit = async (data: SubscriptionFormData) => {
    setIsSubmitting(true);
    
    // Clean up empty strings
    const cleanedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === 'string' && value.trim() === '' ? undefined : value,
      ])
    ) as SubscriptionFormData;

    updateMutation.mutate(cleanedData);
  };

  // Calculate next billing dates for preview
  const getNextBillingDates = () => {
    const billingDate = watch('billing_date');
    if (!billingDate) return [];

    const startDate = new Date(billingDate);
    const dates = [];
    
    for (let i = 0; i < 3; i++) {
      const date = new Date(startDate);
      
      switch (watchedBillingCycle) {
        case 'weekly':
          date.setDate(startDate.getDate() + (i * 7));
          break;
        case 'monthly':
          date.setMonth(startDate.getMonth() + i);
          break;
        case 'quarterly':
          date.setMonth(startDate.getMonth() + (i * 3));
          break;
        case 'yearly':
          date.setFullYear(startDate.getFullYear() + i);
          break;
      }
      
      dates.push(date.toLocaleDateString());
    }
    
    return dates;
  };

  // Get business name
  const getBusinessName = (businessId: string) => {
    const business = businesses.find((b: any) => b.id === businessId);
    return business?.name || 'Unknown Business';
  };

  // Calculate days until next billing
  const getDaysUntilBilling = () => {
    if (!subscription?.next_billing_date) return null;
    
    const nextBilling = new Date(subscription.next_billing_date);
    const today = new Date();
    const diffTime = nextBilling.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading subscription..." />
        </div>
      </Layout>
    );
  }

  if (error || !subscription) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto text-center py-12">
          <ExclamationTriangleIcon className="h-12 w-12 text-error-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-text-primary mb-2">Subscription Not Found</h1>
          <p className="text-text-secondary mb-6">
            The subscription you're looking for doesn't exist or has been deleted.
          </p>
          <Link href="/subscriptions" className="btn-primary">
            Back to Subscriptions
          </Link>
        </div>
      </Layout>
    );
  }

  const status = subscription.status as keyof typeof statusConfig;
  const StatusIcon = statusConfig[status]?.icon || CheckCircleIconSolid;
  const daysUntilBilling = getDaysUntilBilling();

  return (
    <>
      <Head>
        <title>{subscription.name} - SubDash</title>
        <meta name="description" content={`Subscription details for ${subscription.name}`} />
      </Head>

      <Layout>
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Link
                  href="/subscriptions"
                  className="mr-4 p-2 rounded-md text-text-secondary hover:text-text-primary hover:bg-background-secondary transition-colors"
                >
                  <ArrowLeftIcon className="h-5 w-5" />
                </Link>
                <div>
                  <div className="flex items-center gap-3 mb-2">
                    <h1 className="text-2xl font-bold text-text-primary">{subscription.name}</h1>
                    <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      statusConfig[status]?.className || 'text-text-secondary bg-background-secondary'
                    }`}>
                      <StatusIcon className="h-3 w-3 mr-1" />
                      {statusConfig[status]?.label || subscription.status}
                    </div>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-text-secondary">
                    <span className="flex items-center">
                      <BuildingOfficeIcon className="h-4 w-4 mr-1" />
                      {getBusinessName(subscription.business_id)}
                    </span>
                    {subscription.category && (
                      <span className="flex items-center">
                        <TagIcon className="h-4 w-4 mr-1" />
                        {subscription.category}
                      </span>
                    )}
                    <span className="flex items-center">
                      <ClockIcon className="h-4 w-4 mr-1" />
                      Created {formatRelativeTime(subscription.created_at)}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {!isEditing && (
                  <>
                    {subscription.status === 'active' && (
                      <button
                        onClick={() => setShowPauseModal(true)}
                        className="btn-secondary flex items-center"
                      >
                        <PauseIcon className="h-4 w-4 mr-1" />
                        Pause
                      </button>
                    )}
                    
                    {subscription.status === 'paused' && (
                      <button
                        onClick={() => setShowResumeModal(true)}
                        className="btn-secondary flex items-center"
                      >
                        <PlayIcon className="h-4 w-4 mr-1" />
                        Resume
                      </button>
                    )}
                    
                    {subscription.status !== 'cancelled' && (
                      <button
                        onClick={() => setShowCancelModal(true)}
                        className="btn-secondary text-error-600 hover:bg-error-50 dark:hover:bg-error-900 flex items-center"
                      >
                        <XMarkIcon className="h-4 w-4 mr-1" />
                        Cancel
                      </button>
                    )}
                    
                    <button
                      onClick={() => setIsEditing(true)}
                      className="btn-secondary flex items-center"
                    >
                      <PencilIcon className="h-4 w-4 mr-1" />
                      Edit
                    </button>
                    
                    <button
                      onClick={() => setShowDeleteModal(true)}
                      className="btn-secondary text-error-600 hover:bg-error-50 dark:hover:bg-error-900 flex items-center"
                    >
                      <TrashIcon className="h-4 w-4 mr-1" />
                      Delete
                    </button>
                  </>
                )}
                
                {isEditing && (
                  <>
                    <button
                      onClick={() => {
                        setIsEditing(false);
                        reset();
                      }}
                      className="btn-secondary"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSubmit(onSubmit)}
                      disabled={isSubmitting}
                      className="btn-primary flex items-center"
                    >
                      {isSubmitting ? (
                        <>
                          <LoadingSpinner size="sm" className="mr-2" />
                          Saving...
                        </>
                      ) : (
                        'Save Changes'
                      )}
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Overview */}
              {!isEditing && (
                <div className="card">
                  <h2 className="text-lg font-medium text-text-primary mb-4">Overview</h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <div className="text-sm text-text-secondary mb-1">Amount</div>
                      <div className="text-2xl font-bold text-text-primary">
                        {formatCurrency(subscription.amount, subscription.currency)}
                      </div>
                      <div className="text-sm text-text-secondary">
                        per {subscription.billing_cycle}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-sm text-text-secondary mb-1">Next Billing</div>
                      <div className="text-lg font-semibold text-text-primary">
                        {subscription.next_billing_date ? formatDate(subscription.next_billing_date) : 'N/A'}
                      </div>
                      {daysUntilBilling !== null && (
                        <div className={`text-sm ${
                          daysUntilBilling <= 3 ? 'text-warning-600' : 'text-text-secondary'
                        }`}>
                          {daysUntilBilling > 0 ? `${daysUntilBilling} days away` : 
                           daysUntilBilling === 0 ? 'Due today' : 
                           `${Math.abs(daysUntilBilling)} days overdue`}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {subscription.description && (
                    <div className="mt-4 pt-4 border-t border-border-primary">
                      <div className="text-sm text-text-secondary mb-1">Description</div>
                      <div className="text-text-primary">{subscription.description}</div>
                    </div>
                  )}
                  
                  <div className="mt-4 pt-4 border-t border-border-primary grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-text-secondary mb-1">Auto Renew</div>
                      <div className="flex items-center">
                        {subscription.auto_renew ? (
                          <>
                            <CheckCircleIcon className="h-4 w-4 text-success-600 mr-1" />
                            <span className="text-text-primary">Enabled</span>
                          </>
                        ) : (
                          <>
                            <XMarkIcon className="h-4 w-4 text-error-600 mr-1" />
                            <span className="text-text-primary">Disabled</span>
                          </>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-sm text-text-secondary mb-1">Reminder</div>
                      <div className="flex items-center text-text-primary">
                        <BellIcon className="h-4 w-4 mr-1" />
                        {subscription.reminder_days === 0 ? 'No reminder' : 
                         `${subscription.reminder_days} day${subscription.reminder_days > 1 ? 's' : ''} before`}
                      </div>
                    </div>
                  </div>
                  
                  {subscription.website && (
                    <div className="mt-4 pt-4 border-t border-border-primary">
                      <div className="text-sm text-text-secondary mb-1">Website</div>
                      <a
                        href={subscription.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary-600 hover:text-primary-700 flex items-center"
                      >
                        <GlobeAltIcon className="h-4 w-4 mr-1" />
                        {subscription.website}
                      </a>
                    </div>
                  )}
                  
                  {subscription.notes && (
                    <div className="mt-4 pt-4 border-t border-border-primary">
                      <div className="text-sm text-text-secondary mb-1">Notes</div>
                      <div className="text-text-primary whitespace-pre-wrap">{subscription.notes}</div>
                    </div>
                  )}
                </div>
              )}

              {/* Edit Form */}
              {isEditing && (
                <div className="space-y-6">
                  {/* Basic Information */}
                  <div className="card">
                    <h2 className="text-lg font-medium text-text-primary mb-4 flex items-center">
                      <CreditCardIcon className="h-5 w-5 mr-2" />
                      Basic Information
                    </h2>
                    
                    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                      {/* Business Selection */}
                      <div>
                        <label htmlFor="business_id" className="block text-sm font-medium text-text-primary mb-1">
                          Business *
                        </label>
                        <select
                          {...register('business_id')}
                          className={`input-primary w-full ${
                            errors.business_id ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                          }`}
                        >
                          <option value="">Select a business</option>
                          {businesses.map((business: any) => (
                            <option key={business.id} value={business.id}>
                              {business.name}
                            </option>
                          ))}
                        </select>
                        {errors.business_id && (
                          <p className="mt-1 text-sm text-error-600">{errors.business_id.message}</p>
                        )}
                      </div>

                      {/* Subscription Name */}
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-text-primary mb-1">
                          Subscription Name *
                        </label>
                        <input
                          {...register('name')}
                          type="text"
                          className={`input-primary w-full ${
                            errors.name ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                          }`}
                          placeholder="e.g., Adobe Creative Cloud, Slack Pro"
                        />
                        {errors.name && (
                          <p className="mt-1 text-sm text-error-600">{errors.name.message}</p>
                        )}
                      </div>

                      {/* Description */}
                      <div>
                        <label htmlFor="description" className="block text-sm font-medium text-text-primary mb-1">
                          Description
                        </label>
                        <textarea
                          {...register('description')}
                          rows={2}
                          className="input-primary w-full"
                          placeholder="Brief description of the subscription"
                        />
                      </div>

                      {/* Category */}
                      <div>
                        <label htmlFor="category" className="block text-sm font-medium text-text-primary mb-1">
                          Category
                        </label>
                        <select
                          {...register('category')}
                          className="input-primary w-full"
                        >
                          <option value="">Select a category</option>
                          {categories.map((category) => (
                            <option key={category} value={category}>
                              {category}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* Website */}
                      <div>
                        <label htmlFor="website" className="block text-sm font-medium text-text-primary mb-1">
                          Website
                        </label>
                        <input
                          {...register('website')}
                          type="url"
                          className={`input-primary w-full ${
                            errors.website ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                          }`}
                          placeholder="https://example.com"
                        />
                        {errors.website && (
                          <p className="mt-1 text-sm text-error-600">{errors.website.message}</p>
                        )}
                      </div>
                    </form>
                  </div>

                  {/* Billing Information */}
                  <div className="card">
                    <h2 className="text-lg font-medium text-text-primary mb-4 flex items-center">
                      <CurrencyDollarIcon className="h-5 w-5 mr-2" />
                      Billing Information
                    </h2>
                    
                    <div className="space-y-4">
                      {/* Amount and Currency */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label htmlFor="amount" className="block text-sm font-medium text-text-primary mb-1">
                            Amount *
                          </label>
                          <input
                            {...register('amount', { valueAsNumber: true })}
                            type="number"
                            step="0.01"
                            min="0"
                            className={`input-primary w-full ${
                              errors.amount ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                            }`}
                            placeholder="0.00"
                          />
                          {errors.amount && (
                            <p className="mt-1 text-sm text-error-600">{errors.amount.message}</p>
                          )}
                        </div>
                        
                        <div>
                          <label htmlFor="currency" className="block text-sm font-medium text-text-primary mb-1">
                            Currency *
                          </label>
                          <select
                            {...register('currency')}
                            className={`input-primary w-full ${
                              errors.currency ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                            }`}
                          >
                            {currencies.map((currency) => (
                              <option key={currency.code} value={currency.code}>
                                {currency.code} - {currency.name} ({currency.symbol})
                              </option>
                            ))}
                          </select>
                          {errors.currency && (
                            <p className="mt-1 text-sm text-error-600">{errors.currency.message}</p>
                          )}
                        </div>
                      </div>

                      {/* Amount Preview */}
                      {watchedAmount > 0 && (
                        <div className="bg-background-secondary rounded-lg p-3">
                          <div className="text-sm text-text-secondary mb-1">Amount Preview</div>
                          <div className="text-lg font-semibold text-text-primary">
                            {formatCurrency(watchedAmount, watchedCurrency)}
                          </div>
                        </div>
                      )}

                      {/* Billing Cycle */}
                      <div>
                        <label htmlFor="billing_cycle" className="block text-sm font-medium text-text-primary mb-1">
                          Billing Cycle *
                        </label>
                        <select
                          {...register('billing_cycle')}
                          className={`input-primary w-full ${
                            errors.billing_cycle ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                          }`}
                        >
                          {billingCycles.map((cycle) => (
                            <option key={cycle.value} value={cycle.value}>
                              {cycle.label} - {cycle.description}
                            </option>
                          ))}
                        </select>
                        {errors.billing_cycle && (
                          <p className="mt-1 text-sm text-error-600">{errors.billing_cycle.message}</p>
                        )}
                      </div>

                      {/* Next Billing Date */}
                      <div>
                        <label htmlFor="billing_date" className="block text-sm font-medium text-text-primary mb-1">
                          Next Billing Date *
                        </label>
                        <input
                          {...register('billing_date')}
                          type="date"
                          className={`input-primary w-full ${
                            errors.billing_date ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                          }`}
                        />
                        {errors.billing_date && (
                          <p className="mt-1 text-sm text-error-600">{errors.billing_date.message}</p>
                        )}
                      </div>

                      {/* Billing Preview */}
                      {watch('billing_date') && (
                        <div className="bg-background-secondary rounded-lg p-3">
                          <div className="text-sm text-text-secondary mb-2">Next Billing Dates</div>
                          <div className="space-y-1">
                            {getNextBillingDates().map((date, index) => (
                              <div key={index} className="flex items-center text-sm">
                                <CalendarIcon className="h-4 w-4 text-text-tertiary mr-2" />
                                <span className="text-text-primary">{date}</span>
                                {index === 0 && (
                                  <span className="ml-2 text-xs text-primary-600 bg-primary-100 dark:bg-primary-900 px-2 py-0.5 rounded">
                                    Next
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Additional Settings */}
                  <div className="card">
                    <h2 className="text-lg font-medium text-text-primary mb-4 flex items-center">
                      <DocumentTextIcon className="h-5 w-5 mr-2" />
                      Additional Settings
                    </h2>
                    
                    <div className="space-y-4">
                      {/* Auto Renew */}
                      <div className="flex items-center">
                        <input
                          {...register('auto_renew')}
                          type="checkbox"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-border-primary rounded"
                        />
                        <label htmlFor="auto_renew" className="ml-2 block text-sm text-text-primary">
                          Auto-renew subscription
                        </label>
                      </div>

                      {/* Reminder Days */}
                      <div>
                        <label htmlFor="reminder_days" className="block text-sm font-medium text-text-primary mb-1">
                          Reminder Days Before Billing
                        </label>
                        <select
                          {...register('reminder_days', { valueAsNumber: true })}
                          className="input-primary w-full"
                        >
                          <option value={0}>No reminder</option>
                          <option value={1}>1 day before</option>
                          <option value={3}>3 days before</option>
                          <option value={7}>1 week before</option>
                          <option value={14}>2 weeks before</option>
                          <option value={30}>1 month before</option>
                        </select>
                      </div>

                      {/* Notes */}
                      <div>
                        <label htmlFor="notes" className="block text-sm font-medium text-text-primary mb-1">
                          Notes
                        </label>
                        <textarea
                          {...register('notes')}
                          rows={3}
                          className="input-primary w-full"
                          placeholder="Additional notes about this subscription"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Quick Actions */}
              <div className="card">
                <h3 className="text-lg font-medium text-text-primary mb-4">Quick Actions</h3>
                <div className="space-y-2">
                  <Link
                    href={`/payments/new?subscription=${subscription.id}`}
                    className="btn-secondary w-full text-left flex items-center"
                  >
                    <CreditCardIcon className="h-4 w-4 mr-2" />
                    Record Payment
                  </Link>
                  <Link
                    href={`/reimbursements/new?subscription=${subscription.id}`}
                    className="btn-secondary w-full text-left flex items-center"
                  >
                    <DocumentTextIcon className="h-4 w-4 mr-2" />
                    Request Reimbursement
                  </Link>
                  <Link
                    href={`/reports?business=${subscription.business_id}`}
                    className="btn-secondary w-full text-left flex items-center"
                  >
                    <InformationCircleIcon className="h-4 w-4 mr-2" />
                    View Reports
                  </Link>
                </div>
              </div>

              {/* Recent Payments */}
              <div className="card">
                <h3 className="text-lg font-medium text-text-primary mb-4">Recent Payments</h3>
                {recentPayments.length > 0 ? (
                  <div className="space-y-3">
                    {recentPayments.map((payment: any) => (
                      <div key={payment.id} className="flex items-center justify-between py-2 border-b border-border-primary last:border-b-0">
                        <div>
                          <div className="text-sm font-medium text-text-primary">
                            {formatCurrency(payment.amount, payment.currency)}
                          </div>
                          <div className="text-xs text-text-secondary">
                            {formatDate(payment.payment_date)}
                          </div>
                        </div>
                        <div className={`text-xs px-2 py-1 rounded-full ${
                          payment.status === 'completed' ? 'bg-success-100 text-success-700 dark:bg-success-900' :
                          payment.status === 'pending' ? 'bg-warning-100 text-warning-700 dark:bg-warning-900' :
                          'bg-error-100 text-error-700 dark:bg-error-900'
                        }`}>
                          {payment.status}
                        </div>
                      </div>
                    ))}
                    <Link
                      href={`/payments?subscription=${subscription.id}`}
                      className="text-sm text-primary-600 hover:text-primary-700 block mt-2"
                    >
                      View all payments →
                    </Link>
                  </div>
                ) : (
                  <p className="text-sm text-text-secondary">No payments recorded yet.</p>
                )}
              </div>

              {/* Subscription Stats */}
              <div className="card">
                <h3 className="text-lg font-medium text-text-primary mb-4">Statistics</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-text-secondary">Total Paid</span>
                    <span className="text-sm font-medium text-text-primary">
                      {formatCurrency(subscription.total_paid || 0, subscription.currency)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-text-secondary">Payments Count</span>
                    <span className="text-sm font-medium text-text-primary">
                      {subscription.payment_count || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-text-secondary">Active Since</span>
                    <span className="text-sm font-medium text-text-primary">
                      {formatDate(subscription.created_at)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Confirmation Modals */}
        <ConfirmationModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={() => deleteMutation.mutate()}
          title="Delete Subscription"
          message={`Are you sure you want to delete "${subscription.name}"? This action cannot be undone and will also delete all associated payments and data.`}
          confirmText="Delete"
          type="danger"
          loading={deleteMutation.isPending}
        />

        <ConfirmationModal
          isOpen={showPauseModal}
          onClose={() => setShowPauseModal(false)}
          onConfirm={() => pauseMutation.mutate()}
          title="Pause Subscription"
          message={`Are you sure you want to pause "${subscription.name}"? You can resume it later.`}
          confirmText="Pause"
          type="warning"
          loading={pauseMutation.isPending}
        />

        <ConfirmationModal
          isOpen={showResumeModal}
          onClose={() => setShowResumeModal(false)}
          onConfirm={() => resumeMutation.mutate()}
          title="Resume Subscription"
          message={`Are you sure you want to resume "${subscription.name}"?`}
          confirmText="Resume"
          type="info"
          loading={resumeMutation.isPending}
        />

        <ConfirmationModal
          isOpen={showCancelModal}
          onClose={() => setShowCancelModal(false)}
          onConfirm={() => cancelMutation.mutate()}
          title="Cancel Subscription"
          message={`Are you sure you want to cancel "${subscription.name}"? This will stop all future billing and cannot be undone.`}
          confirmText="Cancel Subscription"
          type="danger"
          loading={cancelMutation.isPending}
        />
      </Layout>
    </>
  );
}