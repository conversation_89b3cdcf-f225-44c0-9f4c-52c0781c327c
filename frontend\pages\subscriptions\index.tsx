import { useState, useMemo } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { api } from '../../lib/api';
import { formatCurrency, formatDate, cn } from '../../lib/utils';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EllipsisVerticalIcon,
  PencilIcon,
  TrashIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  ExclamationTriangleIcon,
  CreditCardIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  ChevronUpDownIcon,
} from '@heroicons/react/24/outline';
import {
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/solid';

type SortField = 'name' | 'amount' | 'next_billing_date' | 'status' | 'created_at';
type SortOrder = 'asc' | 'desc';
type StatusFilter = 'all' | 'active' | 'paused' | 'cancelled';

interface Subscription {
  id: string;
  name: string;
  description?: string;
  amount: number;
  currency: string;
  billing_cycle: 'monthly' | 'yearly' | 'weekly' | 'quarterly';
  next_billing_date: string;
  status: 'active' | 'paused' | 'cancelled';
  business_id: string;
  business?: {
    id: string;
    name: string;
  };
  created_at: string;
  updated_at: string;
}

const statusConfig = {
  active: {
    label: 'Active',
    icon: CheckCircleIcon,
    className: 'text-success-600 bg-success-100 dark:bg-success-900 dark:text-success-400',
  },
  paused: {
    label: 'Paused',
    icon: ClockIcon,
    className: 'text-warning-600 bg-warning-100 dark:bg-warning-900 dark:text-warning-400',
  },
  cancelled: {
    label: 'Cancelled',
    icon: XCircleIcon,
    className: 'text-error-600 bg-error-100 dark:bg-error-900 dark:text-error-400',
  },
};

const billingCycleLabels = {
  weekly: 'Weekly',
  monthly: 'Monthly',
  quarterly: 'Quarterly',
  yearly: 'Yearly',
};

export default function SubscriptionsPage() {
  const router = useRouter();
  const { business: businessFilter } = router.query;
  const queryClient = useQueryClient();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');
  const [sortField, setSortField] = useState<SortField>('next_billing_date');
  const [sortOrder, setSortOrder] = useState<SortOrder>('asc');
  const [selectedSubscription, setSelectedSubscription] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch subscriptions
  const { data: subscriptions = [], isLoading, error } = useQuery({
    queryKey: ['subscriptions', businessFilter],
    queryFn: () => {
      const params = businessFilter ? { business_id: businessFilter as string } : {};
      return api.subscriptions.getAll(params);
    },
  });

  // Fetch businesses for filter
  const { data: businesses = [] } = useQuery({
    queryKey: ['businesses'],
    queryFn: () => api.businesses.getAll(),
  });

  // Delete subscription mutation
  const deleteMutation = useMutation({
    mutationFn: (id: string) => api.subscriptions.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Subscription deleted successfully!');
      setShowDeleteModal(false);
      setSelectedSubscription(null);
      setIsDeleting(false);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete subscription');
      setIsDeleting(false);
    },
  });

  // Pause subscription mutation
  const pauseMutation = useMutation({
    mutationFn: (id: string) => api.subscriptions.pause(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Subscription paused successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to pause subscription');
    },
  });

  // Resume subscription mutation
  const resumeMutation = useMutation({
    mutationFn: (id: string) => api.subscriptions.resume(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Subscription resumed successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to resume subscription');
    },
  });

  // Cancel subscription mutation
  const cancelMutation = useMutation({
    mutationFn: (id: string) => api.subscriptions.cancel(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Subscription cancelled successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to cancel subscription');
    },
  });

  // Filter and sort subscriptions
  const filteredAndSortedSubscriptions = useMemo(() => {
    let filtered = subscriptions.filter((subscription: Subscription) => {
      const matchesSearch = subscription.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        subscription.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        subscription.business?.name.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || subscription.status === statusFilter;
      
      return matchesSearch && matchesStatus;
    });

    // Sort subscriptions
    filtered.sort((a: Subscription, b: Subscription) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];

      if (sortField === 'next_billing_date' || sortField === 'created_at') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      } else if (sortField === 'amount') {
        aValue = Number(aValue);
        bValue = Number(bValue);
      } else if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [subscriptions, searchTerm, statusFilter, sortField, sortOrder]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const handleDelete = async () => {
    if (!selectedSubscription) return;
    setIsDeleting(true);
    deleteMutation.mutate(selectedSubscription);
  };

  const handlePause = (id: string) => {
    pauseMutation.mutate(id);
  };

  const handleResume = (id: string) => {
    resumeMutation.mutate(id);
  };

  const handleCancel = (id: string) => {
    cancelMutation.mutate(id);
  };

  const selectedSubscriptionData = subscriptions.find((s: Subscription) => s.id === selectedSubscription);

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading subscriptions..." />
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Subscriptions - SubDash</title>
        <meta name="description" content="Manage your business subscriptions" />
      </Head>

      <Layout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-text-primary">Subscriptions</h1>
              <p className="text-sm text-text-secondary mt-1">
                Manage and track your business subscriptions
              </p>
            </div>
            <Link href="/subscriptions/new" className="btn-primary mt-4 sm:mt-0">
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Subscription
            </Link>
          </div>

          {/* Filters and Search */}
          <div className="card">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-4 w-4 text-text-tertiary" />
                </div>
                <input
                  type="text"
                  placeholder="Search subscriptions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input-primary pl-10 w-full"
                />
              </div>

              {/* Filters */}
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                {/* Status Filter */}
                <div className="flex items-center space-x-2">
                  <FunnelIcon className="h-4 w-4 text-text-tertiary" />
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value as StatusFilter)}
                    className="input-primary text-sm"
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="paused">Paused</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>

                {/* Business Filter */}
                {!businessFilter && (
                  <select
                    value={businessFilter || ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (value) {
                        router.push(`/subscriptions?business=${value}`);
                      } else {
                        router.push('/subscriptions');
                      }
                    }}
                    className="input-primary text-sm"
                  >
                    <option value="">All Businesses</option>
                    {businesses.map((business: any) => (
                      <option key={business.id} value={business.id}>
                        {business.name}
                      </option>
                    ))}
                  </select>
                )}
              </div>
            </div>
          </div>

          {/* Subscriptions Table */}
          <div className="card overflow-hidden">
            {filteredAndSortedSubscriptions.length === 0 ? (
              <div className="text-center py-12">
                <CreditCardIcon className="h-12 w-12 text-text-tertiary mx-auto mb-4" />
                <h3 className="text-lg font-medium text-text-primary mb-2">
                  {subscriptions.length === 0 ? 'No subscriptions found' : 'No matching subscriptions'}
                </h3>
                <p className="text-text-secondary mb-6">
                  {subscriptions.length === 0
                    ? 'Get started by adding your first subscription.'
                    : 'Try adjusting your search or filter criteria.'}
                </p>
                {subscriptions.length === 0 && (
                  <Link href="/subscriptions/new" className="btn-primary">
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add Subscription
                  </Link>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-border-primary">
                  <thead className="bg-background-secondary">
                    <tr>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-background-tertiary transition-colors"
                        onClick={() => handleSort('name')}
                      >
                        <div className="flex items-center space-x-1">
                          <span>Subscription</span>
                          <ChevronUpDownIcon className="h-4 w-4" />
                        </div>
                      </th>
                      {!businessFilter && (
                        <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                          Business
                        </th>
                      )}
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-background-tertiary transition-colors"
                        onClick={() => handleSort('amount')}
                      >
                        <div className="flex items-center space-x-1">
                          <span>Amount</span>
                          <ChevronUpDownIcon className="h-4 w-4" />
                        </div>
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Billing Cycle
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-background-tertiary transition-colors"
                        onClick={() => handleSort('next_billing_date')}
                      >
                        <div className="flex items-center space-x-1">
                          <span>Next Billing</span>
                          <ChevronUpDownIcon className="h-4 w-4" />
                        </div>
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-background-tertiary transition-colors"
                        onClick={() => handleSort('status')}
                      >
                        <div className="flex items-center space-x-1">
                          <span>Status</span>
                          <ChevronUpDownIcon className="h-4 w-4" />
                        </div>
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-background-primary divide-y divide-border-primary">
                    {filteredAndSortedSubscriptions.map((subscription: Subscription) => {
                      const StatusIcon = statusConfig[subscription.status].icon;
                      return (
                        <tr key={subscription.id} className="hover:bg-background-secondary transition-colors">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-text-primary">
                                {subscription.name}
                              </div>
                              {subscription.description && (
                                <div className="text-sm text-text-secondary">
                                  {subscription.description}
                                </div>
                              )}
                            </div>
                          </td>
                          {!businessFilter && (
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <BuildingOfficeIcon className="h-4 w-4 text-text-tertiary mr-2" />
                                <span className="text-sm text-text-primary">
                                  {subscription.business?.name}
                                </span>
                              </div>
                            </td>
                          )}
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-text-primary">
                              {formatCurrency(subscription.amount, subscription.currency)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-text-secondary">
                              {billingCycleLabels[subscription.billing_cycle]}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <CalendarIcon className="h-4 w-4 text-text-tertiary mr-2" />
                              <span className="text-sm text-text-primary">
                                {formatDate(subscription.next_billing_date)}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={cn(
                                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                statusConfig[subscription.status].className
                              )}
                            >
                              <StatusIcon className="h-3 w-3 mr-1" />
                              {statusConfig[subscription.status].label}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex items-center justify-end space-x-2">
                              {/* Status Actions */}
                              {subscription.status === 'active' && (
                                <button
                                  onClick={() => handlePause(subscription.id)}
                                  className="text-warning-600 hover:text-warning-700 p-1 rounded transition-colors"
                                  title="Pause subscription"
                                >
                                  <PauseIcon className="h-4 w-4" />
                                </button>
                              )}
                              {subscription.status === 'paused' && (
                                <button
                                  onClick={() => handleResume(subscription.id)}
                                  className="text-success-600 hover:text-success-700 p-1 rounded transition-colors"
                                  title="Resume subscription"
                                >
                                  <PlayIcon className="h-4 w-4" />
                                </button>
                              )}
                              {subscription.status !== 'cancelled' && (
                                <button
                                  onClick={() => handleCancel(subscription.id)}
                                  className="text-error-600 hover:text-error-700 p-1 rounded transition-colors"
                                  title="Cancel subscription"
                                >
                                  <StopIcon className="h-4 w-4" />
                                </button>
                              )}
                              
                              {/* Edit */}
                              <Link
                                href={`/subscriptions/${subscription.id}`}
                                className="text-primary-600 hover:text-primary-700 p-1 rounded transition-colors"
                                title="Edit subscription"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </Link>
                              
                              {/* Delete */}
                              <button
                                onClick={() => {
                                  setSelectedSubscription(subscription.id);
                                  setShowDeleteModal(true);
                                }}
                                className="text-error-600 hover:text-error-700 p-1 rounded transition-colors"
                                title="Delete subscription"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Summary */}
          {filteredAndSortedSubscriptions.length > 0 && (
            <div className="card">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-text-primary">
                    {filteredAndSortedSubscriptions.length}
                  </div>
                  <div className="text-sm text-text-secondary">Total Subscriptions</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-success-600">
                    {filteredAndSortedSubscriptions.filter((s: any) => s.status === 'active').length}
                  </div>
                  <div className="text-sm text-text-secondary">Active</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-warning-600">
                    {filteredAndSortedSubscriptions.filter((s: any) => s.status === 'paused').length}
                  </div>
                  <div className="text-sm text-text-secondary">Paused</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-error-600">
                    {filteredAndSortedSubscriptions.filter((s: any) => s.status === 'cancelled').length}
                  </div>
                  <div className="text-sm text-text-secondary">Cancelled</div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteModal && selectedSubscriptionData && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-background-primary rounded-lg max-w-md w-full p-6">
              <div className="flex items-center mb-4">
                <ExclamationTriangleIcon className="h-6 w-6 text-error-500 mr-3" />
                <h3 className="text-lg font-medium text-text-primary">Delete Subscription</h3>
              </div>
              <p className="text-text-secondary mb-6">
                Are you sure you want to delete "{selectedSubscriptionData.name}"? This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowDeleteModal(false);
                    setSelectedSubscription(null);
                  }}
                  className="btn-secondary"
                  disabled={isDeleting}
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="btn-danger flex items-center"
                >
                  {isDeleting ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <TrashIcon className="h-4 w-4 mr-2" />
                      Delete Subscription
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </Layout>
    </>
  );
}