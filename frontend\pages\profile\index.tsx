import { useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useQuery } from '@tanstack/react-query';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { useAuth } from '../../hooks/useAuth';
import { api } from '../../lib/api';
import {
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  GlobeAltIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  LanguageIcon,
  ClockIcon,
  PencilIcon,
  ChartBarIcon,
  CreditCardIcon,
  BuildingOfficeIcon,
  DocumentTextIcon,
  BellIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';
import {
  CheckBadgeIcon,
} from '@heroicons/react/24/solid';

export default function ProfilePage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch user profile data
  const { data: profile, isLoading: isProfileLoading } = useQuery({
    queryKey: ['user-profile'],
    queryFn: () => api.users.getProfile(),
  });

  // Fetch user statistics
  const { data: stats, isLoading: isStatsLoading } = useQuery({
    queryKey: ['user-stats'],
    queryFn: () => api.users.getStats(),
  });

  // Fetch recent activity
  const { data: activity, isLoading: isActivityLoading } = useQuery({
    queryKey: ['user-activity'],
    queryFn: () => api.users.getRecentActivity(),
  });

  const isLoading = isProfileLoading || isStatsLoading || isActivityLoading;

  const tabs = [
    { id: 'overview', name: 'Overview', icon: UserIcon },
    { id: 'activity', name: 'Recent Activity', icon: ClockIcon },
    { id: 'statistics', name: 'Statistics', icon: ChartBarIcon },
  ];

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'subscription_created':
      case 'subscription_updated':
        return CreditCardIcon;
      case 'payment_made':
        return CurrencyDollarIcon;
      case 'business_created':
      case 'business_updated':
        return BuildingOfficeIcon;
      case 'reimbursement_submitted':
      case 'reimbursement_approved':
        return DocumentTextIcon;
      default:
        return UserIcon;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'subscription_created':
        return 'text-green-600';
      case 'subscription_updated':
        return 'text-blue-600';
      case 'payment_made':
        return 'text-purple-600';
      case 'business_created':
      case 'business_updated':
        return 'text-orange-600';
      case 'reimbursement_submitted':
        return 'text-yellow-600';
      case 'reimbursement_approved':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading profile..." />
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Profile - SubDash</title>
        <meta name="description" content="View your profile information and account statistics" />
      </Head>

      <Layout>
        <div className="max-w-6xl mx-auto space-y-6">
          {/* Profile Header */}
          <div className="card">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div className="flex items-center space-x-4">
                <div className="h-16 w-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                  <UserIcon className="h-8 w-8 text-primary-600" />
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <h1 className="text-2xl font-bold text-text-primary">
                      {profile?.first_name} {profile?.last_name}
                    </h1>
                    {profile?.email_verified && (
                      <CheckBadgeIcon className="h-6 w-6 text-green-500" title="Verified Account" />
                    )}
                  </div>
                  <p className="text-text-secondary">{profile?.email}</p>
                  <p className="text-sm text-text-tertiary">
                    Member since {formatDate(profile?.created_at || new Date().toISOString())}
                  </p>
                </div>
              </div>
              <div className="mt-4 sm:mt-0">
                <Link href="/settings" className="btn-secondary">
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Edit Profile
                </Link>
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="border-b border-border-primary">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-text-secondary hover:text-text-primary hover:border-border-secondary'
                    }`}
                  >
                    <Icon className="h-5 w-5 mr-2 inline" />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Profile Information */}
              <div className="lg:col-span-2 space-y-6">
                <div className="card">
                  <h2 className="text-lg font-semibold text-text-primary mb-4">Profile Information</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-3">
                      <EnvelopeIcon className="h-5 w-5 text-text-tertiary" />
                      <div>
                        <p className="text-sm text-text-secondary">Email</p>
                        <p className="text-text-primary">{profile?.email || 'Not provided'}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <PhoneIcon className="h-5 w-5 text-text-tertiary" />
                      <div>
                        <p className="text-sm text-text-secondary">Phone</p>
                        <p className="text-text-primary">{profile?.phone || 'Not provided'}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <ClockIcon className="h-5 w-5 text-text-tertiary" />
                      <div>
                        <p className="text-sm text-text-secondary">Timezone</p>
                        <p className="text-text-primary">{profile?.timezone || 'UTC'}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <CurrencyDollarIcon className="h-5 w-5 text-text-tertiary" />
                      <div>
                        <p className="text-sm text-text-secondary">Default Currency</p>
                        <p className="text-text-primary">{profile?.currency || 'USD'}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <LanguageIcon className="h-5 w-5 text-text-tertiary" />
                      <div>
                        <p className="text-sm text-text-secondary">Language</p>
                        <p className="text-text-primary">{profile?.language || 'English'}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <CalendarIcon className="h-5 w-5 text-text-tertiary" />
                      <div>
                        <p className="text-sm text-text-secondary">Last Login</p>
                        <p className="text-text-primary">
                          {profile?.last_login ? formatDate(profile.last_login) : 'Never'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Account Statistics */}
                <div className="card">
                  <h2 className="text-lg font-semibold text-text-primary mb-4">Account Statistics</h2>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary-600">{stats?.total_businesses || 0}</div>
                      <div className="text-sm text-text-secondary">Businesses</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{stats?.total_subscriptions || 0}</div>
                      <div className="text-sm text-text-secondary">Subscriptions</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{stats?.total_payments || 0}</div>
                      <div className="text-sm text-text-secondary">Payments</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">{stats?.total_reimbursements || 0}</div>
                      <div className="text-sm text-text-secondary">Reimbursements</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="space-y-6">
                <div className="card">
                  <h2 className="text-lg font-semibold text-text-primary mb-4">Quick Actions</h2>
                  <div className="space-y-3">
                    <Link href="/businesses/new" className="block w-full btn-primary text-center">
                      <BuildingOfficeIcon className="h-4 w-4 mr-2 inline" />
                      Add Business
                    </Link>
                    <Link href="/subscriptions/new" className="block w-full btn-secondary text-center">
                      <CreditCardIcon className="h-4 w-4 mr-2 inline" />
                      Add Subscription
                    </Link>
                    <Link href="/payments/new" className="block w-full btn-secondary text-center">
                      <CurrencyDollarIcon className="h-4 w-4 mr-2 inline" />
                      Record Payment
                    </Link>
                    <Link href="/reimbursements/new" className="block w-full btn-secondary text-center">
                      <DocumentTextIcon className="h-4 w-4 mr-2 inline" />
                      Submit Reimbursement
                    </Link>
                  </div>
                </div>

                <div className="card">
                  <h2 className="text-lg font-semibold text-text-primary mb-4">Account Settings</h2>
                  <div className="space-y-3">
                    <Link href="/settings" className="flex items-center text-text-secondary hover:text-text-primary transition-colors">
                      <UserIcon className="h-4 w-4 mr-3" />
                      Profile Settings
                    </Link>
                    <Link href="/settings?tab=security" className="flex items-center text-text-secondary hover:text-text-primary transition-colors">
                      <ShieldCheckIcon className="h-4 w-4 mr-3" />
                      Security Settings
                    </Link>
                    <Link href="/settings?tab=notifications" className="flex items-center text-text-secondary hover:text-text-primary transition-colors">
                      <BellIcon className="h-4 w-4 mr-3" />
                      Notifications
                    </Link>
                    <Link href="/reports" className="flex items-center text-text-secondary hover:text-text-primary transition-colors">
                      <ChartBarIcon className="h-4 w-4 mr-3" />
                      View Reports
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'activity' && (
            <div className="card">
              <h2 className="text-lg font-semibold text-text-primary mb-6">Recent Activity</h2>
              {activity && activity.length > 0 ? (
                <div className="space-y-4">
                  {activity.map((item: any, index: number) => {
                    const Icon = getActivityIcon(item.type);
                    return (
                      <div key={index} className="flex items-start space-x-3 p-3 rounded-lg bg-background-secondary">
                        <Icon className={`h-5 w-5 mt-0.5 ${getActivityColor(item.type)}`} />
                        <div className="flex-1">
                          <p className="text-text-primary font-medium">{item.title}</p>
                          <p className="text-text-secondary text-sm">{item.description}</p>
                          <p className="text-text-tertiary text-xs mt-1">
                            {formatDate(item.created_at)}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-8">
                  <ClockIcon className="h-12 w-12 text-text-tertiary mx-auto mb-4" />
                  <p className="text-text-secondary">No recent activity</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'statistics' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Monthly Spending */}
              <div className="card">
                <h3 className="text-lg font-semibold text-text-primary mb-4">Monthly Spending</h3>
                <div className="text-3xl font-bold text-primary-600 mb-2">
                  {formatCurrency(stats?.monthly_spending || 0, profile?.currency)}
                </div>
                <p className="text-text-secondary text-sm">
                  {stats?.monthly_change >= 0 ? '+' : ''}{stats?.monthly_change || 0}% from last month
                </p>
              </div>

              {/* Active Subscriptions */}
              <div className="card">
                <h3 className="text-lg font-semibold text-text-primary mb-4">Active Subscriptions</h3>
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {stats?.active_subscriptions || 0}
                </div>
                <p className="text-text-secondary text-sm">
                  {stats?.subscription_change >= 0 ? '+' : ''}{stats?.subscription_change || 0} this month
                </p>
              </div>

              {/* Total Payments */}
              <div className="card">
                <h3 className="text-lg font-semibold text-text-primary mb-4">Total Payments</h3>
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {formatCurrency(stats?.total_payments_amount || 0, profile?.currency)}
                </div>
                <p className="text-text-secondary text-sm">
                  {stats?.total_payments || 0} transactions
                </p>
              </div>

              {/* Pending Reimbursements */}
              <div className="card">
                <h3 className="text-lg font-semibold text-text-primary mb-4">Pending Reimbursements</h3>
                <div className="text-3xl font-bold text-orange-600 mb-2">
                  {formatCurrency(stats?.pending_reimbursements_amount || 0, profile?.currency)}
                </div>
                <p className="text-text-secondary text-sm">
                  {stats?.pending_reimbursements || 0} requests
                </p>
              </div>

              {/* Average Monthly Spend */}
              <div className="card">
                <h3 className="text-lg font-semibold text-text-primary mb-4">Average Monthly Spend</h3>
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  {formatCurrency(stats?.average_monthly_spend || 0, profile?.currency)}
                </div>
                <p className="text-text-secondary text-sm">
                  Based on last 6 months
                </p>
              </div>

              {/* Account Age */}
              <div className="card">
                <h3 className="text-lg font-semibold text-text-primary mb-4">Account Age</h3>
                <div className="text-3xl font-bold text-indigo-600 mb-2">
                  {stats?.account_age_days || 0}
                </div>
                <p className="text-text-secondary text-sm">
                  Days since registration
                </p>
              </div>
            </div>
          )}
        </div>
      </Layout>
    </>
  );
}