import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Head from 'next/head';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import toast from 'react-hot-toast';
import { useAuth } from '../../hooks/useAuth';
import { useTheme } from '../../hooks/useTheme';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import {
  EyeIcon,
  EyeSlashIcon,
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
} from '@heroicons/react/24/outline';

// Validation schema
const signInSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  rememberMe: z.boolean().optional(),
});

type SignInFormData = z.infer<typeof signInSchema>;

export default function SignIn() {
  const router = useRouter();
  const { signIn, user, loading } = useAuth();
  const { theme, setTheme } = useTheme();
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setFocus,
  } = useForm<SignInFormData>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  // Redirect if already authenticated
  useEffect(() => {
    if (user && !loading) {
      const redirectTo = (router.query.redirect as string) || '/';
      router.replace(redirectTo);
    }
  }, [user, loading, router]);

  // Focus email field on mount
  useEffect(() => {
    setFocus('email');
  }, [setFocus]);

  const onSubmit = async (data: SignInFormData) => {
    setIsSubmitting(true);
    
    try {
      await signIn(data.email, data.password);
      toast.success('Welcome back!');
      
      // Redirect to intended page or dashboard
      const redirectTo = (router.query.redirect as string) || '/';
      router.push(redirectTo);
    } catch (error: any) {
      console.error('Sign in error:', error);
      toast.error(error.message || 'Failed to sign in. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      // This would be implemented with Supabase Google OAuth
      toast.error('Google sign-in not implemented yet');
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign in with Google');
    }
  };

  const themeIcons = {
    light: SunIcon,
    dark: MoonIcon,
    system: ComputerDesktopIcon,
  };

  const ThemeIcon = themeIcons[theme];

  // Show loading spinner while checking auth state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background-primary">
        <LoadingSpinner size="lg" text="Loading..." />
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Sign In - SubDash</title>
        <meta name="description" content="Sign in to your SubDash account" />
      </Head>

      <div className="min-h-screen flex">
        {/* Left side - Form */}
        <div className="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24">
          <div className="mx-auto w-full max-w-sm lg:w-96">
            {/* Header */}
            <div>
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-10 w-10 bg-brand-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-lg">S</span>
                  </div>
                </div>
                <div className="ml-3">
                  <h1 className="text-2xl font-bold text-text-primary">SubDash</h1>
                </div>
              </div>
              <h2 className="mt-6 text-3xl font-bold tracking-tight text-text-primary">
                Welcome back
              </h2>
              <p className="mt-2 text-sm text-text-secondary">
                Don't have an account?{' '}
                <Link
                  href="/auth/signup"
                  className="font-medium text-brand-600 hover:text-brand-500 transition-colors"
                >
                  Sign up for free
                </Link>
              </p>
            </div>

            {/* Form */}
            <div className="mt-8">
              <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
                {/* Email */}
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-text-primary">
                    Email address
                  </label>
                  <div className="mt-1">
                    <input
                      {...register('email')}
                      type="email"
                      autoComplete="email"
                      className={`block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-1 sm:text-sm ${
                        errors.email
                          ? 'border-error-300 focus:border-error-500 focus:ring-error-500'
                          : 'border-border-primary focus:border-brand-500 focus:ring-brand-500'
                      } bg-background-primary text-text-primary`}
                      placeholder="Enter your email"
                    />
                    {errors.email && (
                      <p className="mt-1 text-sm text-error-600">{errors.email.message}</p>
                    )}
                  </div>
                </div>

                {/* Password */}
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-text-primary">
                    Password
                  </label>
                  <div className="mt-1 relative">
                    <input
                      {...register('password')}
                      type={showPassword ? 'text' : 'password'}
                      autoComplete="current-password"
                      className={`block w-full rounded-md border px-3 py-2 pr-10 shadow-sm focus:outline-none focus:ring-1 sm:text-sm ${
                        errors.password
                          ? 'border-error-300 focus:border-error-500 focus:ring-error-500'
                          : 'border-border-primary focus:border-brand-500 focus:ring-brand-500'
                      } bg-background-primary text-text-primary`}
                      placeholder="Enter your password"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeSlashIcon className="h-5 w-5 text-text-tertiary hover:text-text-secondary" />
                      ) : (
                        <EyeIcon className="h-5 w-5 text-text-tertiary hover:text-text-secondary" />
                      )}
                    </button>
                    {errors.password && (
                      <p className="mt-1 text-sm text-error-600">{errors.password.message}</p>
                    )}
                  </div>
                </div>

                {/* Remember me & Forgot password */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <input
                      {...register('rememberMe')}
                      type="checkbox"
                      className="h-4 w-4 rounded border-border-primary text-brand-600 focus:ring-brand-500"
                    />
                    <label htmlFor="rememberMe" className="ml-2 block text-sm text-text-secondary">
                      Remember me
                    </label>
                  </div>

                  <div className="text-sm">
                    <Link
                      href="/auth/forgot-password"
                      className="font-medium text-brand-600 hover:text-brand-500 transition-colors"
                    >
                      Forgot your password?
                    </Link>
                  </div>
                </div>

                {/* Submit button */}
                <div>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-brand-600 hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isSubmitting ? (
                      <LoadingSpinner size="sm" className="text-white" />
                    ) : (
                      'Sign in'
                    )}
                  </button>
                </div>

                {/* Divider */}
                <div className="mt-6">
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-border-primary" />
                    </div>
                    <div className="relative flex justify-center text-sm">
                      <span className="px-2 bg-background-primary text-text-secondary">Or continue with</span>
                    </div>
                  </div>
                </div>

                {/* Social sign in */}
                <div>
                  <button
                    type="button"
                    onClick={handleGoogleSignIn}
                    className="w-full inline-flex justify-center py-2 px-4 border border-border-primary rounded-md shadow-sm bg-background-primary text-sm font-medium text-text-primary hover:bg-background-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500 transition-colors"
                  >
                    <svg className="w-5 h-5" viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      />
                      <path
                        fill="currentColor"
                        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      />
                      <path
                        fill="currentColor"
                        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      />
                      <path
                        fill="currentColor"
                        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      />
                    </svg>
                    <span className="ml-2">Sign in with Google</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>

        {/* Right side - Image/Branding */}
        <div className="hidden lg:block relative w-0 flex-1">
          <div className="absolute inset-0 bg-gradient-to-br from-brand-600 to-brand-800">
            <div className="absolute inset-0 bg-black opacity-20" />
            <div className="relative h-full flex flex-col justify-center items-center text-white p-12">
              <div className="max-w-md text-center">
                <h2 className="text-4xl font-bold mb-6">
                  Manage Your Subscriptions
                </h2>
                <p className="text-xl opacity-90 mb-8">
                  Take control of your business expenses with comprehensive subscription tracking and analytics.
                </p>
                <div className="grid grid-cols-1 gap-4 text-left">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0 w-2 h-2 bg-white rounded-full" />
                    <span>Track all your subscriptions in one place</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0 w-2 h-2 bg-white rounded-full" />
                    <span>Get notified before renewals</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0 w-2 h-2 bg-white rounded-full" />
                    <span>Analyze spending patterns</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0 w-2 h-2 bg-white rounded-full" />
                    <span>Manage reimbursements efficiently</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Theme toggle */}
        <div className="absolute top-4 right-4">
          <button
            onClick={() => {
              const themes = ['light', 'dark', 'system'] as const;
              const currentIndex = themes.indexOf(theme);
              const nextTheme = themes[(currentIndex + 1) % themes.length];
              setTheme(nextTheme);
            }}
            className="p-2 rounded-md text-text-secondary hover:text-text-primary hover:bg-background-secondary transition-colors"
            title={`Current theme: ${theme}. Click to switch.`}
          >
            <ThemeIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    </>
  );
}