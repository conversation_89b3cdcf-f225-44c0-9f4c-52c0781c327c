"use client"

import { useState, useEffect } from "react"
import { Receipt, Plus, CheckCircle, XCircle, Clock } from "lucide-react"
import { format } from "date-fns"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { ReimbursementForm } from "@/components/reimbursement-form"
import { SharedLayout } from "@/components/shared-layout"
import { PaymentRecord, ReimbursementRecord } from "@/types"

export function ReimbursementsPage() {
  const [reimbursements, setReimbursements] = useState<ReimbursementRecord[]>([])
  const [payments, setPayments] = useState<PaymentRecord[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  useEffect(() => {
    const storedReimbursements = JSON.parse(localStorage.getItem("reimbursements") || "[]")
    const storedPayments = JSON.parse(localStorage.getItem("payments") || "[]")
    setReimbursements(storedReimbursements)
    setPayments(storedPayments)
  }, [isDialogOpen])

  const getPaymentDetails = (paymentId: string) => {
    return payments.find((p) => p.id === paymentId)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "rejected":
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>
      case "rejected":
        return <Badge className="bg-red-100 text-red-800">Rejected</Badge>
      default:
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
    }
  }

  const totalPendingKwd = reimbursements.filter((r) => r.status === "pending").reduce((sum, r) => sum + r.kwdAmount, 0)

  const totalApprovedKwd = reimbursements
    .filter((r) => r.status === "approved")
    .reduce((sum, r) => sum + r.kwdAmount, 0)

  return (
    <SharedLayout activeUrl="/reimbursements">
      <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Reimbursements</h1>
            <p className="text-muted-foreground">Manage your subscription reimbursement requests (KWD only)</p>
          </div>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Request Reimbursement
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Request Reimbursement</DialogTitle>
                <DialogDescription>Submit a reimbursement request for a subscription payment</DialogDescription>
              </DialogHeader>
              <ReimbursementForm />
            </DialogContent>
          </Dialog>
        </div>

        {/* Summary Cards */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
              <Receipt className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{reimbursements.length}</div>
              <p className="text-xs text-muted-foreground">All time</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending (KWD)</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalPendingKwd.toFixed(2)} KWD</div>
              <p className="text-xs text-muted-foreground">
                {reimbursements.filter((r) => r.status === "pending").length} requests
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Approved (KWD)</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalApprovedKwd.toFixed(2)} KWD</div>
              <p className="text-xs text-muted-foreground">
                {reimbursements.filter((r) => r.status === "approved").length} approved
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Reimbursements List */}
        <Card>
          <CardHeader>
            <CardTitle>Reimbursement Requests</CardTitle>
            <CardDescription>Your subscription reimbursement history (all amounts in KWD)</CardDescription>
          </CardHeader>
          <CardContent>
            {reimbursements.length === 0 ? (
              <div className="text-center py-8">
                <Receipt className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-4 text-lg font-medium">No reimbursement requests</h3>
                <p className="text-muted-foreground">Submit your first reimbursement request</p>
              </div>
            ) : (
              <div className="space-y-4">
                {reimbursements.map((reimbursement) => {
                  const payment = getPaymentDetails(reimbursement.linkedPaymentId)
                  return (
                    <div
                      key={reimbursement.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                          {getStatusIcon(reimbursement.status)}
                        </div>
                        <div>
                          <h4 className="font-medium">{payment?.subscriptionName || "Unknown Subscription"}</h4>
                          <p className="text-sm text-muted-foreground">
                            Requested: {format(new Date(reimbursement.requestDate), "MMM dd, yyyy")}
                          </p>
                          {payment && (
                            <p className="text-xs text-muted-foreground">
                              Original: {payment.originalAmount.toFixed(2)} {payment.originalCurrency} → Rate:{" "}
                              {payment.exchangeRate.toFixed(3)} KWD
                            </p>
                          )}
                        </div>
                      </div>

                      <div className="text-right space-y-1">
                        <div className="font-bold text-lg">{reimbursement.kwdAmount.toFixed(2)} KWD</div>
                        {getStatusBadge(reimbursement.status)}
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </SharedLayout>
  )
}
