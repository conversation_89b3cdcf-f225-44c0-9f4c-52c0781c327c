import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import toast from 'react-hot-toast';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import ConfirmationModal from '../../components/ui/ConfirmationModal';
import { api } from '../../lib/api';
import { formatCurrency, formatDate, formatRelativeTime } from '../../lib/utils';
import {
  CreditCardIcon,
  BuildingOfficeIcon,
  DocumentTextIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  InformationCircleIcon,
  PencilIcon,
  TrashIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  EyeIcon,
  LinkIcon,
} from '@heroicons/react/24/outline';
import {
  CheckCircleIcon as CheckCircleIconSolid,
  ClockIcon as ClockIconSolid,
  XCircleIcon as XCircleIconSolid,
} from '@heroicons/react/24/solid';

// Form validation schema
const paymentSchema = z.object({
  subscription_id: z.string().min(1, 'Please select a subscription'),
  business_id: z.string().min(1, 'Please select a business'),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  currency: z.string().min(1, 'Please select a currency'),
  payment_date: z.string().min(1, 'Payment date is required'),
  payment_method: z.enum(['credit_card', 'bank_transfer', 'paypal', 'other'], {
    required_error: 'Please select a payment method',
  }),
  status: z.enum(['completed', 'pending', 'failed'], {
    required_error: 'Please select a status',
  }),
  description: z.string().optional(),
  transaction_id: z.string().optional(),
  notes: z.string().optional(),
});

type PaymentFormData = z.infer<typeof paymentSchema>;

// Common currencies
const currencies = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
];

// Payment methods
const paymentMethods = [
  { value: 'credit_card', label: 'Credit Card', icon: CreditCardIcon },
  { value: 'bank_transfer', label: 'Bank Transfer', icon: BuildingOfficeIcon },
  { value: 'paypal', label: 'PayPal', icon: DocumentTextIcon },
  { value: 'other', label: 'Other', icon: DocumentTextIcon },
];

// Payment statuses
const paymentStatuses = [
  { value: 'completed', label: 'Completed', description: 'Payment has been successfully processed' },
  { value: 'pending', label: 'Pending', description: 'Payment is being processed' },
  { value: 'failed', label: 'Failed', description: 'Payment failed to process' },
];

// Status configurations
const statusConfig = {
  completed: {
    label: 'Completed',
    icon: CheckCircleIconSolid,
    className: 'text-success-600 bg-success-100 dark:bg-success-900',
  },
  pending: {
    label: 'Pending',
    icon: ClockIconSolid,
    className: 'text-warning-600 bg-warning-100 dark:bg-warning-900',
  },
  failed: {
    label: 'Failed',
    icon: XCircleIconSolid,
    className: 'text-error-600 bg-error-100 dark:bg-error-900',
  },
};

export default function PaymentDetailPage() {
  const router = useRouter();
  const { id } = router.query;
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [filteredSubscriptions, setFilteredSubscriptions] = useState<any[]>([]);

  // Fetch payment details
  const { data: paymentResponse, isLoading: paymentLoading } = useQuery({
    queryKey: ['payment', id],
    queryFn: () => api.payments.getById(id as string),
    enabled: !!id,
  });

  // Fetch businesses
  const { data: businessesResponse } = useQuery({
    queryKey: ['businesses'],
    queryFn: () => api.businesses.getAll(),
  });

  // Fetch subscriptions
  const { data: subscriptionsResponse } = useQuery({
    queryKey: ['subscriptions'],
    queryFn: () => api.subscriptions.getAll(),
  });

  // Extract data from API responses
  const payment = paymentResponse?.data;
  const businesses = businessesResponse?.data || [];
  const subscriptions = subscriptionsResponse?.data || [];

  // Form setup
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
  });

  const watchedBusinessId = watch('business_id');
  const watchedSubscriptionId = watch('subscription_id');

  // Initialize form with payment data
  useEffect(() => {
    if (payment) {
      reset({
        subscription_id: payment.subscription_id,
        business_id: payment.business_id,
        amount: payment.amount,
        currency: payment.currency,
        payment_date: payment.payment_date.split('T')[0],
        payment_method: payment.payment_method,
        status: payment.status,
        description: payment.description || '',
        transaction_id: payment.transaction_id || '',
        notes: payment.notes || '',
      });
    }
  }, [payment, reset]);

  // Filter subscriptions based on selected business
  useEffect(() => {
    if (watchedBusinessId) {
      const filtered = subscriptions.filter(
        (sub: any) => sub.business_id === watchedBusinessId
      );
      setFilteredSubscriptions(filtered);
    } else {
      setFilteredSubscriptions([]);
    }
  }, [watchedBusinessId, subscriptions]);

  // Update payment mutation
  const updateMutation = useMutation({
    mutationFn: (data: PaymentFormData) => api.payments.update(id as string, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payment', id] });
      queryClient.invalidateQueries({ queryKey: ['payments'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Payment updated successfully!');
      setIsEditing(false);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update payment');
    },
  });

  // Delete payment mutation
  const deleteMutation = useMutation({
    mutationFn: () => api.payments.delete(id as string),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payments'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Payment deleted successfully!');
      router.push('/payments');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete payment');
    },
  });

  const onSubmit = (data: PaymentFormData) => {
    updateMutation.mutate(data);
  };

  const handleDelete = () => {
    deleteMutation.mutate();
  };

  const getBusinessName = (businessId: string) => {
    const business = businesses.find((b: any) => b.id === businessId);
    return business?.name || 'Unknown Business';
  };

  const getSubscriptionName = (subscriptionId: string) => {
    const subscription = subscriptions.find((s: any) => s.id === subscriptionId);
    return subscription?.name || 'Unknown Subscription';
  };

  const getPaymentMethodLabel = (method: string) => {
    const paymentMethod = paymentMethods.find((m) => m.value === method);
    return paymentMethod?.label || method;
  };

  if (paymentLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading payment..." />
        </div>
      </Layout>
    );
  }

  if (!payment) {
    return (
      <Layout>
        <div className="text-center py-12">
          <XCircleIcon className="h-12 w-12 text-text-tertiary mx-auto mb-4" />
          <h3 className="text-lg font-medium text-text-primary mb-2">Payment not found</h3>
          <p className="text-text-secondary mb-6">
            The payment you're looking for doesn't exist or has been deleted.
          </p>
          <Link href="/payments" className="btn-primary">
            Back to Payments
          </Link>
        </div>
      </Layout>
    );
  }

  const status = payment.status as keyof typeof statusConfig;
  const StatusIcon = statusConfig[status]?.icon || CheckCircleIconSolid;
  const paymentMethod = payment.payment_method as keyof typeof paymentMethods;
  const MethodIcon = paymentMethods.find(m => m.value === paymentMethod)?.icon || DocumentTextIcon;

  return (
    <>
      <Head>
        <title>{`Payment - ${getSubscriptionName(payment.subscription_id)} - SubDash`}</title>
        <meta name="description" content={`Payment details for ${getSubscriptionName(payment.subscription_id)}`} />
      </Head>

      <Layout>
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="mr-4 p-2 text-text-secondary hover:text-text-primary transition-colors"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-text-primary">
                  Payment Details
                </h1>
                <p className="text-sm text-text-secondary mt-1">
                  {getSubscriptionName(payment.subscription_id)} • {getBusinessName(payment.business_id)}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              {!isEditing ? (
                <>
                  <button
                    onClick={() => setIsEditing(true)}
                    className="btn-secondary flex items-center"
                  >
                    <PencilIcon className="h-4 w-4 mr-2" />
                    Edit
                  </button>
                  <button
                    onClick={() => setShowDeleteModal(true)}
                    className="btn-danger flex items-center"
                  >
                    <TrashIcon className="h-4 w-4 mr-2" />
                    Delete
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={() => {
                      setIsEditing(false);
                      reset();
                    }}
                    className="btn-secondary"
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSubmit(onSubmit)}
                    disabled={isSubmitting}
                    className="btn-primary flex items-center"
                  >
                    {isSubmitting ? (
                      <>
                        <LoadingSpinner size="sm" className="mr-2" />
                        Saving...
                      </>
                    ) : (
                      'Save Changes'
                    )}
                  </button>
                </>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Payment Overview */}
              <div className="card">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-text-primary">Payment Overview</h3>
                  <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    statusConfig[status]?.className || 'text-text-secondary bg-background-secondary'
                  }`}>
                    <StatusIcon className="h-4 w-4 mr-1" />
                    {statusConfig[status]?.label || payment.status}
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="text-center md:text-left">
                    <div className="text-3xl font-bold text-primary-600 mb-2">
                      {formatCurrency(payment.amount, payment.currency)}
                    </div>
                    <div className="text-text-secondary">
                      Paid on {formatDate(payment.payment_date)}
                    </div>
                    <div className="text-sm text-text-tertiary mt-1">
                      {formatRelativeTime(payment.payment_date)}
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center text-sm">
                      <MethodIcon className="h-4 w-4 mr-2 text-text-tertiary" />
                      <span className="text-text-secondary">Payment Method:</span>
                      <span className="ml-2 text-text-primary font-medium">
                        {getPaymentMethodLabel(payment.payment_method)}
                      </span>
                    </div>
                    
                    {payment.transaction_id && (
                      <div className="flex items-center text-sm">
                        <LinkIcon className="h-4 w-4 mr-2 text-text-tertiary" />
                        <span className="text-text-secondary">Transaction ID:</span>
                        <span className="ml-2 text-text-primary font-mono text-xs">
                          {payment.transaction_id}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                
                {payment.description && (
                  <div className="mt-4 pt-4 border-t border-border-primary">
                    <p className="text-text-primary">{payment.description}</p>
                  </div>
                )}
              </div>

              {/* Edit Form */}
              {isEditing && (
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {/* Business and Subscription */}
                  <div className="card">
                    <h3 className="text-lg font-medium text-text-primary mb-4">Business & Subscription</h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Business *
                        </label>
                        <select
                          {...register('business_id')}
                          className={`input-primary w-full ${
                            errors.business_id ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        >
                          <option value="">Select a business</option>
                          {businesses.map((business: any) => (
                            <option key={business.id} value={business.id}>
                              {business.name}
                            </option>
                          ))}
                        </select>
                        {errors.business_id && (
                          <p className="text-error-600 text-sm mt-1">{errors.business_id.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Subscription *
                        </label>
                        <select
                          {...register('subscription_id')}
                          className={`input-primary w-full ${
                            errors.subscription_id ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        >
                          <option value="">Select a subscription</option>
                          {filteredSubscriptions.map((subscription: any) => (
                            <option key={subscription.id} value={subscription.id}>
                              {subscription.name} - {formatCurrency(subscription.amount, subscription.currency)}
                            </option>
                          ))}
                        </select>
                        {errors.subscription_id && (
                          <p className="text-error-600 text-sm mt-1">{errors.subscription_id.message}</p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Payment Details */}
                  <div className="card">
                    <h3 className="text-lg font-medium text-text-primary mb-4">Payment Details</h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Amount *
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          min="0"
                          {...register('amount', { valueAsNumber: true })}
                          className={`input-primary w-full ${
                            errors.amount ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        />
                        {errors.amount && (
                          <p className="text-error-600 text-sm mt-1">{errors.amount.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Currency *
                        </label>
                        <select
                          {...register('currency')}
                          className={`input-primary w-full ${
                            errors.currency ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        >
                          {currencies.map((currency) => (
                            <option key={currency.code} value={currency.code}>
                              {currency.code} - {currency.name}
                            </option>
                          ))}
                        </select>
                        {errors.currency && (
                          <p className="text-error-600 text-sm mt-1">{errors.currency.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Payment Date *
                        </label>
                        <input
                          type="date"
                          {...register('payment_date')}
                          className={`input-primary w-full ${
                            errors.payment_date ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        />
                        {errors.payment_date && (
                          <p className="text-error-600 text-sm mt-1">{errors.payment_date.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Payment Method *
                        </label>
                        <select
                          {...register('payment_method')}
                          className={`input-primary w-full ${
                            errors.payment_method ? 'border-error-500 focus:border-error-500' : ''
                          }`}
                        >
                          {paymentMethods.map((method) => (
                            <option key={method.value} value={method.value}>
                              {method.label}
                            </option>
                          ))}
                        </select>
                        {errors.payment_method && (
                          <p className="text-error-600 text-sm mt-1">{errors.payment_method.message}</p>
                        )}
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Status *
                        </label>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                          {paymentStatuses.map((statusOption) => (
                            <label key={statusOption.value} className="relative">
                              <input
                                type="radio"
                                {...register('status')}
                                value={statusOption.value}
                                className="sr-only peer"
                              />
                              <div className="p-3 border border-border-primary rounded-lg cursor-pointer transition-all peer-checked:border-primary-500 peer-checked:bg-primary-50 dark:peer-checked:bg-primary-900/20 hover:border-primary-300">
                                <div className="font-medium text-text-primary">{statusOption.label}</div>
                                <div className="text-sm text-text-secondary mt-1">{statusOption.description}</div>
                              </div>
                            </label>
                          ))}
                        </div>
                        {errors.status && (
                          <p className="text-error-600 text-sm mt-1">{errors.status.message}</p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Additional Information */}
                  <div className="card">
                    <h3 className="text-lg font-medium text-text-primary mb-4">Additional Information</h3>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Description
                        </label>
                        <input
                          type="text"
                          {...register('description')}
                          className="input-primary w-full"
                          placeholder="Brief description of the payment"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Transaction ID
                        </label>
                        <input
                          type="text"
                          {...register('transaction_id')}
                          className="input-primary w-full"
                          placeholder="Payment processor transaction ID"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                          Notes
                        </label>
                        <textarea
                          {...register('notes')}
                          rows={3}
                          className="input-primary w-full"
                          placeholder="Additional notes about this payment"
                        />
                      </div>
                    </div>
                  </div>
                </form>
              )}

              {/* Notes (View Mode) */}
              {!isEditing && payment.notes && (
                <div className="card">
                  <h3 className="text-lg font-medium text-text-primary mb-4">Notes</h3>
                  <p className="text-text-secondary whitespace-pre-wrap">{payment.notes}</p>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Quick Actions */}
              <div className="card">
                <h3 className="text-lg font-medium text-text-primary mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <Link
                    href={`/subscriptions/${payment.subscription_id}`}
                    className="flex items-center w-full p-3 text-left text-text-secondary hover:text-primary-600 hover:bg-background-secondary rounded-lg transition-colors"
                  >
                    <EyeIcon className="h-4 w-4 mr-3" />
                    View Subscription
                  </Link>
                  
                  <Link
                    href={`/businesses/${payment.business_id}`}
                    className="flex items-center w-full p-3 text-left text-text-secondary hover:text-primary-600 hover:bg-background-secondary rounded-lg transition-colors"
                  >
                    <BuildingOfficeIcon className="h-4 w-4 mr-3" />
                    View Business
                  </Link>
                  
                  <Link
                    href="/payments/new"
                    className="flex items-center w-full p-3 text-left text-text-secondary hover:text-primary-600 hover:bg-background-secondary rounded-lg transition-colors"
                  >
                    <CreditCardIcon className="h-4 w-4 mr-3" />
                    Record New Payment
                  </Link>
                </div>
              </div>

              {/* Payment Information */}
              <div className="card">
                <h3 className="text-lg font-medium text-text-primary mb-4">Payment Information</h3>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Created:</span>
                    <span className="text-text-primary">{formatDate(payment.created_at)}</span>
                  </div>
                  
                  {payment.updated_at !== payment.created_at && (
                    <div className="flex justify-between">
                      <span className="text-text-secondary">Updated:</span>
                      <span className="text-text-primary">{formatDate(payment.updated_at)}</span>
                    </div>
                  )}
                  
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Payment ID:</span>
                    <span className="text-text-primary font-mono text-xs">{payment.id}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        <ConfirmationModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={handleDelete}
          title="Delete Payment"
          message="Are you sure you want to delete this payment? This action cannot be undone."
          confirmText="Delete"
          type="danger"
          loading={deleteMutation.isPending}
        />
      </Layout>
    </>
  );
}