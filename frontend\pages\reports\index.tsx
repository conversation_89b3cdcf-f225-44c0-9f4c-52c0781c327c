import { useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { api } from '../../lib/api';
import { formatCurrency, formatDate, formatPercentage } from '../../lib/utils';
import {
  ChartBarIcon,
  DocumentArrowDownIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  BuildingOfficeIcon,
  CreditCardIcon,
  ReceiptRefundIcon,
  ArrowPathIcon,
  FunnelIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend,
} from 'recharts';

// Filter form schema
const filterSchema = z.object({
  business_id: z.string().optional(),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  report_type: z.enum(['overview', 'subscriptions', 'payments', 'reimbursements']),
});

type FilterFormData = z.infer<typeof filterSchema>;

// Chart colors
const COLORS = {
  primary: '#3B82F6',
  secondary: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  purple: '#8B5CF6',
  pink: '#EC4899',
  indigo: '#6366F1',
  teal: '#14B8A6',
};

const PIE_COLORS = [COLORS.primary, COLORS.secondary, COLORS.warning, COLORS.error, COLORS.purple, COLORS.pink];

export default function ReportsPage() {
  const [showFilters, setShowFilters] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  const {
    register,
    watch,
    setValue,
    handleSubmit,
    formState: { errors },
  } = useForm<FilterFormData>({
    resolver: zodResolver(filterSchema),
    defaultValues: {
      report_type: 'overview',
      start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
      end_date: new Date().toISOString().split('T')[0], // Today
    },
  });

  const filters = watch();

  // Fetch businesses for filter
  const { data: businesses = [] } = useQuery({
    queryKey: ['businesses'],
    queryFn: () => api.businesses.getAll(),
  });

  // Fetch report data
  const { data: reportData, isLoading, refetch } = useQuery({
    queryKey: ['reports', filters],
    queryFn: () => api.reports.getReports(filters),
  });

  // Export report
  const handleExport = async (format: 'pdf' | 'csv') => {
    setIsExporting(true);
    try {
      const blob = await api.reports.exportReport({ ...filters, format });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `report-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const onFilterSubmit = () => {
    refetch();
  };

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-border-primary rounded-lg shadow-lg">
          <p className="text-text-primary font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {typeof entry.value === 'number' && entry.name.includes('Amount') 
                ? formatCurrency(entry.value, 'USD')
                : entry.value
              }
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading reports..." />
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Reports & Analytics - SubDash</title>
        <meta name="description" content="View detailed reports and analytics for your subscriptions, payments, and expenses" />
      </Head>

      <Layout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-text-primary">Reports & Analytics</h1>
              <p className="text-text-secondary mt-1">
                Analyze your subscription spending and track financial trends
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`btn-secondary flex items-center ${
                  showFilters ? 'bg-primary-100 text-primary-700 dark:bg-primary-900' : ''
                }`}
              >
                <FunnelIcon className="h-4 w-4 mr-2" />
                Filters
              </button>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleExport('csv')}
                  disabled={isExporting}
                  className="btn-secondary flex items-center"
                >
                  <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                  Export CSV
                </button>
                
                <button
                  onClick={() => handleExport('pdf')}
                  disabled={isExporting}
                  className="btn-primary flex items-center"
                >
                  {isExporting ? (
                    <LoadingSpinner size="sm" className="mr-2" />
                  ) : (
                    <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                  )}
                  Export PDF
                </button>
              </div>
            </div>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="card">
              <form onSubmit={handleSubmit(onFilterSubmit)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Report Type
                    </label>
                    <select
                      {...register('report_type')}
                      className="input-primary w-full"
                    >
                      <option value="overview">Overview</option>
                      <option value="subscriptions">Subscriptions</option>
                      <option value="payments">Payments</option>
                      <option value="reimbursements">Reimbursements</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Business
                    </label>
                    <select
                      {...register('business_id')}
                      className="input-primary w-full"
                    >
                      <option value="">All Businesses</option>
                      {businesses.map((business: any) => (
                        <option key={business.id} value={business.id}>
                          {business.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Start Date
                    </label>
                    <input
                      type="date"
                      {...register('start_date')}
                      className="input-primary w-full"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      End Date
                    </label>
                    <input
                      type="date"
                      {...register('end_date')}
                      className="input-primary w-full"
                    />
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => {
                      setValue('business_id', '');
                      setValue('start_date', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]);
                      setValue('end_date', new Date().toISOString().split('T')[0]);
                      setValue('report_type', 'overview');
                    }}
                    className="btn-secondary"
                  >
                    Reset
                  </button>
                  <button type="submit" className="btn-primary flex items-center">
                    <ArrowPathIcon className="h-4 w-4 mr-2" />
                    Apply Filters
                  </button>
                </div>
              </form>
            </div>
          )}

          {reportData && (
            <>
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="card">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-text-secondary">Total Spend</p>
                      <p className="text-2xl font-bold text-text-primary">
                        {formatCurrency(reportData.summary?.total_spend || 0, 'USD')}
                      </p>
                      {reportData.summary?.spend_change && (
                        <div className="flex items-center mt-1">
                          {reportData.summary.spend_change > 0 ? (
                            <TrendingUpIcon className="h-4 w-4 text-error-500 mr-1" />
                          ) : (
                            <TrendingDownIcon className="h-4 w-4 text-success-500 mr-1" />
                          )}
                          <span className={`text-sm ${
                            reportData.summary.spend_change > 0 ? 'text-error-600' : 'text-success-600'
                          }`}>
                            {formatPercentage(Math.abs(reportData.summary.spend_change))}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="p-3 bg-primary-100 dark:bg-primary-900 rounded-lg">
                      <CurrencyDollarIcon className="h-6 w-6 text-primary-600" />
                    </div>
                  </div>
                </div>
                
                <div className="card">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-text-secondary">Active Subscriptions</p>
                      <p className="text-2xl font-bold text-text-primary">
                        {reportData.summary?.active_subscriptions || 0}
                      </p>
                      {reportData.summary?.subscription_change && (
                        <div className="flex items-center mt-1">
                          {reportData.summary.subscription_change > 0 ? (
                            <TrendingUpIcon className="h-4 w-4 text-success-500 mr-1" />
                          ) : (
                            <TrendingDownIcon className="h-4 w-4 text-error-500 mr-1" />
                          )}
                          <span className={`text-sm ${
                            reportData.summary.subscription_change > 0 ? 'text-success-600' : 'text-error-600'
                          }`}>
                            {Math.abs(reportData.summary.subscription_change)}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="p-3 bg-secondary-100 dark:bg-secondary-900 rounded-lg">
                      <CreditCardIcon className="h-6 w-6 text-secondary-600" />
                    </div>
                  </div>
                </div>
                
                <div className="card">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-text-secondary">Total Payments</p>
                      <p className="text-2xl font-bold text-text-primary">
                        {reportData.summary?.total_payments || 0}
                      </p>
                      <p className="text-sm text-text-secondary mt-1">
                        {formatCurrency(reportData.summary?.payment_amount || 0, 'USD')}
                      </p>
                    </div>
                    <div className="p-3 bg-warning-100 dark:bg-warning-900 rounded-lg">
                      <CreditCardIcon className="h-6 w-6 text-warning-600" />
                    </div>
                  </div>
                </div>
                
                <div className="card">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-text-secondary">Reimbursements</p>
                      <p className="text-2xl font-bold text-text-primary">
                        {reportData.summary?.total_reimbursements || 0}
                      </p>
                      <p className="text-sm text-text-secondary mt-1">
                        {formatCurrency(reportData.summary?.reimbursement_amount || 0, 'USD')}
                      </p>
                    </div>
                    <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-lg">
                      <ReceiptRefundIcon className="h-6 w-6 text-purple-600" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Charts */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Spending Trend Chart */}
                {reportData.spending_trend && reportData.spending_trend.length > 0 && (
                  <div className="card">
                    <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center">
                      <ChartBarIcon className="h-5 w-5 mr-2" />
                      Spending Trend
                    </h3>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={reportData.spending_trend}>
                          <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                          <XAxis 
                            dataKey="date" 
                            stroke="#6B7280"
                            fontSize={12}
                          />
                          <YAxis 
                            stroke="#6B7280"
                            fontSize={12}
                            tickFormatter={(value) => formatCurrency(value, 'USD', true)}
                          />
                          <Tooltip content={<CustomTooltip />} />
                          <Line 
                            type="monotone" 
                            dataKey="amount" 
                            stroke={COLORS.primary} 
                            strokeWidth={2}
                            dot={{ fill: COLORS.primary, strokeWidth: 2, r: 4 }}
                            activeDot={{ r: 6, stroke: COLORS.primary, strokeWidth: 2 }}
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                )}

                {/* Category Breakdown */}
                {reportData.category_breakdown && reportData.category_breakdown.length > 0 && (
                  <div className="card">
                    <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center">
                      <ChartBarIcon className="h-5 w-5 mr-2" />
                      Spending by Category
                    </h3>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={reportData.category_breakdown}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="amount"
                          >
                            {reportData.category_breakdown.map((entry: any, index: number) => (
                              <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip 
                            formatter={(value: any) => [formatCurrency(value, 'USD'), 'Amount']}
                          />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                )}

                {/* Monthly Comparison */}
                {reportData.monthly_comparison && reportData.monthly_comparison.length > 0 && (
                  <div className="card lg:col-span-2">
                    <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center">
                      <ChartBarIcon className="h-5 w-5 mr-2" />
                      Monthly Comparison
                    </h3>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={reportData.monthly_comparison}>
                          <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                          <XAxis 
                            dataKey="month" 
                            stroke="#6B7280"
                            fontSize={12}
                          />
                          <YAxis 
                            stroke="#6B7280"
                            fontSize={12}
                            tickFormatter={(value) => formatCurrency(value, 'USD', true)}
                          />
                          <Tooltip content={<CustomTooltip />} />
                          <Bar dataKey="subscriptions" fill={COLORS.primary} name="Subscriptions" />
                          <Bar dataKey="payments" fill={COLORS.secondary} name="Payments" />
                          <Bar dataKey="reimbursements" fill={COLORS.warning} name="Reimbursements" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                )}
              </div>

              {/* Top Subscriptions */}
              {reportData.top_subscriptions && reportData.top_subscriptions.length > 0 && (
                <div className="card">
                  <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center">
                    <CreditCardIcon className="h-5 w-5 mr-2" />
                    Top Subscriptions by Spend
                  </h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-border-primary">
                      <thead className="bg-background-secondary">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                            Subscription
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                            Business
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                            Monthly Cost
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                            Total Spend
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-background-primary divide-y divide-border-primary">
                        {reportData.top_subscriptions.map((subscription: any) => (
                          <tr key={subscription.id} className="hover:bg-background-secondary">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-text-primary">
                                  {subscription.name}
                                </div>
                                <div className="text-sm text-text-secondary">
                                  {subscription.description}
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <BuildingOfficeIcon className="h-4 w-4 text-text-tertiary mr-2" />
                                <span className="text-sm text-text-primary">
                                  {subscription.business_name}
                                </span>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="text-sm font-medium text-text-primary">
                                {formatCurrency(subscription.amount, subscription.currency)}
                              </span>
                              <span className="text-xs text-text-secondary ml-1">
                                /{subscription.billing_cycle}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="text-sm font-semibold text-text-primary">
                                {formatCurrency(subscription.total_spend, subscription.currency)}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <Link
                                href={`/subscriptions/${subscription.id}`}
                                className="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center"
                              >
                                <EyeIcon className="h-4 w-4 mr-1" />
                                View
                              </Link>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Recent Activity */}
              {reportData.recent_activity && reportData.recent_activity.length > 0 && (
                <div className="card">
                  <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center">
                    <CalendarIcon className="h-5 w-5 mr-2" />
                    Recent Activity
                  </h3>
                  <div className="space-y-4">
                    {reportData.recent_activity.map((activity: any, index: number) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-background-secondary rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`p-2 rounded-lg ${
                            activity.type === 'subscription' ? 'bg-primary-100 dark:bg-primary-900' :
                            activity.type === 'payment' ? 'bg-secondary-100 dark:bg-secondary-900' :
                            'bg-warning-100 dark:bg-warning-900'
                          }`}>
                            {activity.type === 'subscription' ? (
                              <CreditCardIcon className={`h-4 w-4 ${
                                activity.type === 'subscription' ? 'text-primary-600' :
                                activity.type === 'payment' ? 'text-secondary-600' :
                                'text-warning-600'
                              }`} />
                            ) : activity.type === 'payment' ? (
                              <CurrencyDollarIcon className="h-4 w-4 text-secondary-600" />
                            ) : (
                              <ReceiptRefundIcon className="h-4 w-4 text-warning-600" />
                            )}
                          </div>
                          <div>
                            <p className="text-sm font-medium text-text-primary">
                              {activity.description}
                            </p>
                            <p className="text-xs text-text-secondary">
                              {formatDate(activity.date)}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-semibold text-text-primary">
                            {formatCurrency(activity.amount, activity.currency)}
                          </p>
                          <p className="text-xs text-text-secondary capitalize">
                            {activity.type}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}

          {/* Empty State */}
          {!reportData && !isLoading && (
            <div className="text-center py-12">
              <ChartBarIcon className="h-12 w-12 text-text-tertiary mx-auto mb-4" />
              <h3 className="text-lg font-medium text-text-primary mb-2">No Data Available</h3>
              <p className="text-text-secondary mb-6">
                No data found for the selected filters. Try adjusting your date range or filters.
              </p>
              <button
                onClick={() => refetch()}
                className="btn-primary flex items-center mx-auto"
              >
                <ArrowPathIcon className="h-4 w-4 mr-2" />
                Refresh Data
              </button>
            </div>
          )}
        </div>
      </Layout>
    </>
  );
}