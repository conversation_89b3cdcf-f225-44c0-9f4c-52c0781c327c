import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Head from 'next/head';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import toast from 'react-hot-toast';
import { useAuth } from '../../hooks/useAuth';
import { useTheme } from '../../hooks/useTheme';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import {
  ArrowLeftIcon,
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
  EnvelopeIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

// Validation schema
const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

export default function ForgotPassword() {
  const router = useRouter();
  const { resetPassword, user, loading } = useAuth();
  const { theme, setTheme } = useTheme();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [sentEmail, setSentEmail] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    setFocus,
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  // Redirect if already authenticated
  useEffect(() => {
    if (user && !loading) {
      router.replace('/');
    }
  }, [user, loading, router]);

  // Focus email field on mount
  useEffect(() => {
    if (!emailSent) {
      setFocus('email');
    }
  }, [setFocus, emailSent]);

  const onSubmit = async (data: ForgotPasswordFormData) => {
    setIsSubmitting(true);
    
    try {
      await resetPassword(data.email);
      setSentEmail(data.email);
      setEmailSent(true);
      toast.success('Password reset email sent!');
    } catch (error: any) {
      console.error('Reset password error:', error);
      toast.error(error.message || 'Failed to send reset email. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendEmail = async () => {
    if (!sentEmail) return;
    
    setIsSubmitting(true);
    try {
      await resetPassword(sentEmail);
      toast.success('Password reset email sent again!');
    } catch (error: any) {
      toast.error(error.message || 'Failed to resend email.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const themeIcons = {
    light: SunIcon,
    dark: MoonIcon,
    system: ComputerDesktopIcon,
  };

  const ThemeIcon = themeIcons[theme];

  // Show loading spinner while checking auth state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background-primary">
        <LoadingSpinner size="lg" text="Loading..." />
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Forgot Password - SubDash</title>
        <meta name="description" content="Reset your SubDash password" />
      </Head>

      <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-background-primary">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          {/* Header */}
          <div className="text-center">
            <div className="flex justify-center">
              <div className="h-12 w-12 bg-brand-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">S</span>
              </div>
            </div>
            <h1 className="mt-4 text-3xl font-bold text-text-primary">SubDash</h1>
          </div>

          {!emailSent ? (
            <>
              <h2 className="mt-6 text-center text-2xl font-bold tracking-tight text-text-primary">
                Forgot your password?
              </h2>
              <p className="mt-2 text-center text-sm text-text-secondary">
                No worries! Enter your email address and we'll send you a link to reset your password.
              </p>
            </>
          ) : (
            <>
              <div className="mt-6 text-center">
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-success-100">
                  <CheckCircleIcon className="h-8 w-8 text-success-600" />
                </div>
                <h2 className="mt-4 text-2xl font-bold tracking-tight text-text-primary">
                  Check your email
                </h2>
                <p className="mt-2 text-sm text-text-secondary">
                  We've sent a password reset link to{' '}
                  <span className="font-medium text-text-primary">{sentEmail}</span>
                </p>
              </div>
            </>
          )}
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-background-secondary py-8 px-4 shadow-lg sm:rounded-lg sm:px-10 border border-border-primary">
            {!emailSent ? (
              <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
                {/* Email */}
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-text-primary">
                    Email address
                  </label>
                  <div className="mt-1 relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <EnvelopeIcon className="h-5 w-5 text-text-tertiary" />
                    </div>
                    <input
                      {...register('email')}
                      type="email"
                      autoComplete="email"
                      className={`block w-full pl-10 pr-3 py-2 rounded-md border shadow-sm focus:outline-none focus:ring-1 sm:text-sm ${
                        errors.email
                          ? 'border-error-300 focus:border-error-500 focus:ring-error-500'
                          : 'border-border-primary focus:border-brand-500 focus:ring-brand-500'
                      } bg-background-primary text-text-primary`}
                      placeholder="Enter your email address"
                    />
                    {errors.email && (
                      <p className="mt-1 text-sm text-error-600">{errors.email.message}</p>
                    )}
                  </div>
                </div>

                {/* Submit button */}
                <div>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-brand-600 hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isSubmitting ? (
                      <LoadingSpinner size="sm" className="text-white" />
                    ) : (
                      'Send reset link'
                    )}
                  </button>
                </div>
              </form>
            ) : (
              <div className="space-y-6">
                {/* Instructions */}
                <div className="text-center space-y-4">
                  <div className="bg-background-primary border border-border-primary rounded-lg p-4">
                    <h3 className="text-sm font-medium text-text-primary mb-2">
                      What's next?
                    </h3>
                    <div className="text-sm text-text-secondary space-y-2">
                      <p>1. Check your email inbox (and spam folder)</p>
                      <p>2. Click the reset link in the email</p>
                      <p>3. Create a new password</p>
                      <p>4. Sign in with your new password</p>
                    </div>
                  </div>
                  
                  <p className="text-xs text-text-tertiary">
                    The reset link will expire in 1 hour for security reasons.
                  </p>
                </div>

                {/* Resend button */}
                <div>
                  <button
                    type="button"
                    onClick={handleResendEmail}
                    disabled={isSubmitting}
                    className="w-full flex justify-center py-2 px-4 border border-border-primary rounded-md shadow-sm text-sm font-medium text-text-primary bg-background-primary hover:bg-background-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isSubmitting ? (
                      <LoadingSpinner size="sm" />
                    ) : (
                      'Resend email'
                    )}
                  </button>
                </div>

                {/* Try different email */}
                <div className="text-center">
                  <button
                    type="button"
                    onClick={() => {
                      setEmailSent(false);
                      setSentEmail('');
                    }}
                    className="text-sm font-medium text-brand-600 hover:text-brand-500 transition-colors"
                  >
                    Try a different email address
                  </button>
                </div>
              </div>
            )}

            {/* Back to sign in */}
            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-border-primary" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-background-secondary text-text-secondary">or</span>
                </div>
              </div>
            </div>

            <div className="mt-6">
              <Link
                href="/auth/signin"
                className="w-full flex justify-center items-center py-2 px-4 border border-border-primary rounded-md shadow-sm text-sm font-medium text-text-primary bg-background-primary hover:bg-background-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500 transition-colors"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to sign in
              </Link>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-xs text-text-tertiary">
            Need help?{' '}
            <Link href="/support" className="font-medium text-brand-600 hover:text-brand-500">
              Contact support
            </Link>
          </p>
        </div>

        {/* Theme toggle */}
        <div className="absolute top-4 right-4">
          <button
            onClick={() => {
              const themes = ['light', 'dark', 'system'] as const;
              const currentIndex = themes.indexOf(theme);
              const nextTheme = themes[(currentIndex + 1) % themes.length];
              setTheme(nextTheme);
            }}
            className="p-2 rounded-md text-text-secondary hover:text-text-primary hover:bg-background-secondary transition-colors"
            title={`Current theme: ${theme}. Click to switch.`}
          >
            <ThemeIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    </>
  );
}