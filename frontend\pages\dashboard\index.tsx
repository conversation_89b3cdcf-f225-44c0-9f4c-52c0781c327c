import { useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useQuery } from '@tanstack/react-query';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { api } from '../../lib/api';
import {
  CurrencyDollarIcon,
  CreditCardIcon,
  BuildingOfficeIcon,
  DocumentTextIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeIcon,
  PlusIcon,
  ChartBarIcon,
  CalendarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

export default function DashboardPage() {
  const [timeRange, setTimeRange] = useState('30d');

  // Fetch dashboard data
  const { data: dashboardResponse, isLoading } = useQuery({
    queryKey: ['dashboard', timeRange],
    queryFn: () => api.dashboard.getOverview(timeRange),
  });

  // Fetch recent activity
  const { data: recentActivityResponse } = useQuery({
    queryKey: ['dashboard-activity'],
    queryFn: () => api.dashboard.getRecentActivity(),
  });

  // Fetch upcoming renewals
  const { data: upcomingRenewalsResponse } = useQuery({
    queryKey: ['dashboard-renewals'],
    queryFn: () => api.dashboard.getUpcomingRenewals(),
  });

  // Extract data from API responses
  const dashboardData = dashboardResponse?.data;
  const recentActivity = recentActivityResponse?.data || [];
  const upcomingRenewals = upcomingRenewalsResponse?.data || [];

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
      case 'paid':
      case 'approved':
        return 'text-green-600 bg-green-100 dark:bg-green-900';
      case 'paused':
      case 'pending':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900';
      case 'cancelled':
      case 'failed':
      case 'rejected':
        return 'text-red-600 bg-red-100 dark:bg-red-900';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900';
    }
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) {
      return <ArrowUpIcon className="h-4 w-4 text-green-500" />;
    } else if (change < 0) {
      return <ArrowDownIcon className="h-4 w-4 text-red-500" />;
    }
    return null;
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading dashboard..." />
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Dashboard - SubDash</title>
        <meta name="description" content="Overview of your subscription management dashboard" />
      </Head>

      <Layout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-text-primary">Dashboard</h1>
              <p className="text-text-secondary mt-1">
                Welcome back! Here's what's happening with your subscriptions.
              </p>
            </div>
            <div className="mt-4 sm:mt-0 flex items-center space-x-3">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="input-primary"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="1y">Last year</option>
              </select>
              <Link href="/subscriptions/new" className="btn-primary">
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Subscription
              </Link>
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-text-secondary text-sm font-medium">Total Spend</p>
                  <p className="text-2xl font-bold text-text-primary">
                    {formatCurrency(dashboardData?.totalSpend || 0)}
                  </p>
                  <div className="flex items-center mt-1">
                    {getChangeIcon(dashboardData?.spendChange || 0)}
                    <span className={`text-sm ml-1 ${getChangeColor(dashboardData?.spendChange || 0)}`}>
                      {Math.abs(dashboardData?.spendChange || 0)}%
                    </span>
                  </div>
                </div>
                <div className="h-12 w-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                  <CurrencyDollarIcon className="h-6 w-6 text-primary-600" />
                </div>
              </div>
            </div>

            <div className="card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-text-secondary text-sm font-medium">Active Subscriptions</p>
                  <p className="text-2xl font-bold text-text-primary">
                    {dashboardData?.activeSubscriptions || 0}
                  </p>
                  <div className="flex items-center mt-1">
                    {getChangeIcon(dashboardData?.subscriptionChange || 0)}
                    <span className={`text-sm ml-1 ${getChangeColor(dashboardData?.subscriptionChange || 0)}`}>
                      {Math.abs(dashboardData?.subscriptionChange || 0)}
                    </span>
                  </div>
                </div>
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                  <CreditCardIcon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-text-secondary text-sm font-medium">Businesses</p>
                  <p className="text-2xl font-bold text-text-primary">
                    {dashboardData?.totalBusinesses || 0}
                  </p>
                  <div className="flex items-center mt-1">
                    {getChangeIcon(dashboardData?.businessChange || 0)}
                    <span className={`text-sm ml-1 ${getChangeColor(dashboardData?.businessChange || 0)}`}>
                      {Math.abs(dashboardData?.businessChange || 0)}
                    </span>
                  </div>
                </div>
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                  <BuildingOfficeIcon className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </div>

            <div className="card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-text-secondary text-sm font-medium">Pending Reimbursements</p>
                  <p className="text-2xl font-bold text-text-primary">
                    {formatCurrency(dashboardData?.pendingReimbursements || 0)}
                  </p>
                  <div className="flex items-center mt-1">
                    <span className="text-sm text-text-secondary">
                      {dashboardData?.pendingReimbursementCount || 0} requests
                    </span>
                  </div>
                </div>
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                  <DocumentTextIcon className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Spending Trend Chart */}
            <div className="lg:col-span-2 card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-semibold text-text-primary">Spending Trend</h2>
                <Link href="/reports" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                  View Reports
                </Link>
              </div>
              {dashboardData?.spendingTrend && dashboardData.spendingTrend.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={dashboardData.spendingTrend}>
                    <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                    <XAxis 
                      dataKey="date" 
                      tickFormatter={formatDate}
                      className="text-text-secondary"
                    />
                    <YAxis 
                      tickFormatter={(value) => formatCurrency(value).replace('.00', '')}
                      className="text-text-secondary"
                    />
                    <Tooltip 
                      formatter={(value: any) => [formatCurrency(value), 'Amount']}
                      labelFormatter={(label) => formatDate(label)}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="amount" 
                      stroke="#3B82F6" 
                      fill="#3B82F6" 
                      fillOpacity={0.1}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-[300px] text-text-secondary">
                  <div className="text-center">
                    <ChartBarIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No spending data available</p>
                  </div>
                </div>
              )}
            </div>

            {/* Upcoming Renewals */}
            <div className="card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-semibold text-text-primary">Upcoming Renewals</h2>
                <Link href="/subscriptions" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                  View All
                </Link>
              </div>
              {upcomingRenewals && upcomingRenewals.length > 0 ? (
                <div className="space-y-3">
                  {upcomingRenewals.slice(0, 5).map((renewal: any) => (
                    <div key={renewal.id} className="flex items-center justify-between p-3 bg-background-secondary rounded-lg">
                      <div className="flex-1">
                        <p className="font-medium text-text-primary text-sm">{renewal.name}</p>
                        <p className="text-text-secondary text-xs">{renewal.business_name}</p>
                        <p className="text-text-tertiary text-xs">
                          {formatDate(renewal.next_billing_date)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-text-primary text-sm">
                          {formatCurrency(renewal.amount, renewal.currency)}
                        </p>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          getStatusColor(renewal.status)
                        }`}>
                          {renewal.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <CalendarIcon className="h-12 w-12 text-text-tertiary mx-auto mb-4 opacity-50" />
                  <p className="text-text-secondary">No upcoming renewals</p>
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Category Breakdown */}
            <div className="card">
              <h2 className="text-lg font-semibold text-text-primary mb-6">Spending by Category</h2>
              {dashboardData?.categoryBreakdown && dashboardData.categoryBreakdown.length > 0 ? (
                <div className="flex flex-col lg:flex-row items-center">
                  <div className="w-full lg:w-1/2">
                    <ResponsiveContainer width="100%" height={200}>
                      <PieChart>
                        <Pie
                          data={dashboardData.categoryBreakdown}
                          cx="50%"
                          cy="50%"
                          innerRadius={40}
                          outerRadius={80}
                          paddingAngle={5}
                          dataKey="amount"
                        >
                          {dashboardData.categoryBreakdown.map((entry: any, index: number) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value: any) => formatCurrency(value)} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  <div className="w-full lg:w-1/2 mt-4 lg:mt-0">
                    <div className="space-y-2">
                      {dashboardData.categoryBreakdown.map((category: any, index: number) => (
                        <div key={category.name} className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div 
                              className="w-3 h-3 rounded-full mr-2"
                              style={{ backgroundColor: COLORS[index % COLORS.length] }}
                            />
                            <span className="text-sm text-text-primary">{category.name}</span>
                          </div>
                          <span className="text-sm font-medium text-text-primary">
                            {formatCurrency(category.amount)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <ChartBarIcon className="h-12 w-12 text-text-tertiary mx-auto mb-4 opacity-50" />
                  <p className="text-text-secondary">No category data available</p>
                </div>
              )}
            </div>

            {/* Recent Activity */}
            <div className="card">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-semibold text-text-primary">Recent Activity</h2>
                <Link href="/profile?tab=activity" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                  View All
                </Link>
              </div>
              {recentActivity && recentActivity.length > 0 ? (
                <div className="space-y-3">
                  {recentActivity.slice(0, 5).map((activity: any, index: number) => {
                    const getActivityIcon = (type: string) => {
                      switch (type) {
                        case 'subscription_created':
                          return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
                        case 'payment_made':
                          return <CurrencyDollarIcon className="h-5 w-5 text-blue-500" />;
                        case 'subscription_cancelled':
                          return <XCircleIcon className="h-5 w-5 text-red-500" />;
                        case 'reimbursement_submitted':
                          return <DocumentTextIcon className="h-5 w-5 text-orange-500" />;
                        default:
                          return <ClockIcon className="h-5 w-5 text-gray-500" />;
                      }
                    };

                    return (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-0.5">
                          {getActivityIcon(activity.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm text-text-primary font-medium">
                            {activity.title}
                          </p>
                          <p className="text-xs text-text-secondary">
                            {activity.description}
                          </p>
                          <p className="text-xs text-text-tertiary mt-1">
                            {formatDate(activity.created_at)}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-8">
                  <ClockIcon className="h-12 w-12 text-text-tertiary mx-auto mb-4 opacity-50" />
                  <p className="text-text-secondary">No recent activity</p>
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="card">
            <h2 className="text-lg font-semibold text-text-primary mb-6">Quick Actions</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <Link href="/businesses/new" className="btn-secondary flex items-center justify-center">
                <BuildingOfficeIcon className="h-5 w-5 mr-2" />
                Add Business
              </Link>
              <Link href="/subscriptions/new" className="btn-secondary flex items-center justify-center">
                <CreditCardIcon className="h-5 w-5 mr-2" />
                Add Subscription
              </Link>
              <Link href="/payments/new" className="btn-secondary flex items-center justify-center">
                <CurrencyDollarIcon className="h-5 w-5 mr-2" />
                Record Payment
              </Link>
              <Link href="/reimbursements/new" className="btn-secondary flex items-center justify-center">
                <DocumentTextIcon className="h-5 w-5 mr-2" />
                Submit Reimbursement
              </Link>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
}