import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable');
}

if (!supabaseAnonKey) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable');
}

// Create Supabase client with additional configuration
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    // Configure auth settings
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    // Storage configuration for auth tokens
    storage: typeof window !== 'undefined' ? window.localStorage : undefined,
  },
  // Global configuration
  global: {
    headers: {
      'X-Client-Info': 'subdash-frontend',
    },
  },
  // Database configuration
  db: {
    schema: 'public',
  },
  // Realtime configuration
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});

// Database types (will be generated from Supabase)
export interface Database {
  public: {
    Tables: {
      businesses: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          description: string | null;
          industry: string | null;
          website: string | null;
          phone: string | null;
          email: string | null;
          address: string | null;
          city: string | null;
          country: string;
          currency: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          description?: string | null;
          industry?: string | null;
          website?: string | null;
          phone?: string | null;
          email?: string | null;
          address?: string | null;
          city?: string | null;
          country?: string;
          currency?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          description?: string | null;
          industry?: string | null;
          website?: string | null;
          phone?: string | null;
          email?: string | null;
          address?: string | null;
          city?: string | null;
          country?: string;
          currency?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      subscriptions: {
        Row: {
          id: string;
          business_id: string;
          name: string;
          description: string | null;
          amount: number;
          currency: string;
          billing_cycle: 'weekly' | 'monthly' | 'quarterly' | 'yearly';
          start_date: string;
          end_date: string | null;
          next_billing_date: string;
          status: 'active' | 'paused' | 'cancelled';
          category: string | null;
          vendor: string | null;
          payment_method: string | null;
          auto_renew: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          business_id: string;
          name: string;
          description?: string | null;
          amount: number;
          currency?: string;
          billing_cycle: 'weekly' | 'monthly' | 'quarterly' | 'yearly';
          start_date: string;
          end_date?: string | null;
          next_billing_date: string;
          status?: 'active' | 'paused' | 'cancelled';
          category?: string | null;
          vendor?: string | null;
          payment_method?: string | null;
          auto_renew?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          business_id?: string;
          name?: string;
          description?: string | null;
          amount?: number;
          currency?: string;
          billing_cycle?: 'weekly' | 'monthly' | 'quarterly' | 'yearly';
          start_date?: string;
          end_date?: string | null;
          next_billing_date?: string;
          status?: 'active' | 'paused' | 'cancelled';
          category?: string | null;
          vendor?: string | null;
          payment_method?: string | null;
          auto_renew?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      payments: {
        Row: {
          id: string;
          business_id: string;
          subscription_id: string | null;
          amount: number;
          currency: string;
          payment_date: string;
          payment_method: string | null;
          status: 'pending' | 'completed' | 'failed' | 'refunded';
          description: string | null;
          reference_number: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          business_id: string;
          subscription_id?: string | null;
          amount: number;
          currency?: string;
          payment_date: string;
          payment_method?: string | null;
          status?: 'pending' | 'completed' | 'failed' | 'refunded';
          description?: string | null;
          reference_number?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          business_id?: string;
          subscription_id?: string | null;
          amount?: number;
          currency?: string;
          payment_date?: string;
          payment_method?: string | null;
          status?: 'pending' | 'completed' | 'failed' | 'refunded';
          description?: string | null;
          reference_number?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      reimbursements: {
        Row: {
          id: string;
          business_id: string;
          amount: number;
          currency: string;
          description: string;
          category: string | null;
          receipt_url: string | null;
          status: 'pending' | 'approved' | 'rejected' | 'paid';
          submitted_date: string;
          approved_date: string | null;
          paid_date: string | null;
          notes: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          business_id: string;
          amount: number;
          currency?: string;
          description: string;
          category?: string | null;
          receipt_url?: string | null;
          status?: 'pending' | 'approved' | 'rejected' | 'paid';
          submitted_date: string;
          approved_date?: string | null;
          paid_date?: string | null;
          notes?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          business_id?: string;
          amount?: number;
          currency?: string;
          description?: string;
          category?: string | null;
          receipt_url?: string | null;
          status?: 'pending' | 'approved' | 'rejected' | 'paid';
          submitted_date?: string;
          approved_date?: string | null;
          paid_date?: string | null;
          notes?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      exchange_rates: {
        Row: {
          id: string;
          from_currency: string;
          to_currency: string;
          rate: number;
          date: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          from_currency: string;
          to_currency: string;
          rate: number;
          date: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          from_currency?: string;
          to_currency?: string;
          rate?: number;
          date?: string;
          created_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}

// Helper functions for common database operations
export const dbHelpers = {
  // Businesses
  async getBusinesses(userId: string) {
    const { data, error } = await supabase
      .from('businesses')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  },

  async getBusiness(id: string, userId: string) {
    const { data, error } = await supabase
      .from('businesses')
      .select('*')
      .eq('id', id)
      .eq('user_id', userId)
      .single();
    
    if (error) throw error;
    return data;
  },

  async createBusiness(business: Database['public']['Tables']['businesses']['Insert']) {
    const { data, error } = await supabase
      .from('businesses')
      .insert(business)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async updateBusiness(id: string, updates: Database['public']['Tables']['businesses']['Update']) {
    const { data, error } = await supabase
      .from('businesses')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async deleteBusiness(id: string) {
    const { error } = await supabase
      .from('businesses')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  },

  // Subscriptions
  async getSubscriptions(businessId: string) {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('business_id', businessId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  },

  async getSubscription(id: string) {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },

  async createSubscription(subscription: Database['public']['Tables']['subscriptions']['Insert']) {
    const { data, error } = await supabase
      .from('subscriptions')
      .insert(subscription)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async updateSubscription(id: string, updates: Database['public']['Tables']['subscriptions']['Update']) {
    const { data, error } = await supabase
      .from('subscriptions')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async deleteSubscription(id: string) {
    const { error } = await supabase
      .from('subscriptions')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  },

  // Payments
  async getPayments(businessId: string) {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        *,
        subscriptions(name)
      `)
      .eq('business_id', businessId)
      .order('payment_date', { ascending: false });
    
    if (error) throw error;
    return data;
  },

  async createPayment(payment: Database['public']['Tables']['payments']['Insert']) {
    const { data, error } = await supabase
      .from('payments')
      .insert(payment)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  // Reimbursements
  async getReimbursements(businessId: string) {
    const { data, error } = await supabase
      .from('reimbursements')
      .select('*')
      .eq('business_id', businessId)
      .order('submitted_date', { ascending: false });
    
    if (error) throw error;
    return data;
  },

  async createReimbursement(reimbursement: Database['public']['Tables']['reimbursements']['Insert']) {
    const { data, error } = await supabase
      .from('reimbursements')
      .insert(reimbursement)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  // Exchange rates
  async getExchangeRate(fromCurrency: string, toCurrency: string, date?: string) {
    let query = supabase
      .from('exchange_rates')
      .select('*')
      .eq('from_currency', fromCurrency)
      .eq('to_currency', toCurrency)
      .order('date', { ascending: false })
      .limit(1);
    
    if (date) {
      query = query.eq('date', date);
    }
    
    const { data, error } = await query.single();
    
    if (error) throw error;
    return data;
  },
};

// Real-time subscriptions helpers
export const realtimeHelpers = {
  subscribeToBusinesses(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel('businesses')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'businesses',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  },

  subscribeToSubscriptions(businessId: string, callback: (payload: any) => void) {
    return supabase
      .channel('subscriptions')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'subscriptions',
          filter: `business_id=eq.${businessId}`,
        },
        callback
      )
      .subscribe();
  },

  subscribeToPayments(businessId: string, callback: (payload: any) => void) {
    return supabase
      .channel('payments')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'payments',
          filter: `business_id=eq.${businessId}`,
        },
        callback
      )
      .subscribe();
  },

  subscribeToReimbursements(businessId: string, callback: (payload: any) => void) {
    return supabase
      .channel('reimbursements')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'reimbursements',
          filter: `business_id=eq.${businessId}`,
        },
        callback
      )
      .subscribe();
  },
};