import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { api } from '../../lib/api';
import { formatCurrency } from '../../lib/utils';
import {
  ArrowLeftIcon,
  CreditCardIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  BuildingOfficeIcon,
  DocumentTextIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';

// Validation schema
const subscriptionSchema = z.object({
  name: z.string().min(2, 'Subscription name must be at least 2 characters'),
  description: z.string().optional(),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  currency: z.string().min(3, 'Please select a currency'),
  billing_cycle: z.enum(['weekly', 'monthly', 'quarterly', 'yearly'], {
    required_error: 'Please select a billing cycle',
  }),
  billing_date: z.string().min(1, 'Please select a billing date'),
  business_id: z.string().min(1, 'Please select a business'),
  category: z.string().optional(),
  website: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  notes: z.string().optional(),
  auto_renew: z.boolean().default(true),
  reminder_days: z.number().min(0).max(30).default(3),
});

type SubscriptionFormData = z.infer<typeof subscriptionSchema>;

// Common currencies
const currencies = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'KWD', name: 'Kuwaiti Dinar', symbol: 'د.ك' },
  { code: 'SAR', name: 'Saudi Riyal', symbol: 'ر.س' },
  { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ' },
  { code: 'QAR', name: 'Qatari Riyal', symbol: 'ر.ق' },
  { code: 'BHD', name: 'Bahraini Dinar', symbol: '.د.ب' },
  { code: 'OMR', name: 'Omani Rial', symbol: 'ر.ع.' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
];

// Billing cycles
const billingCycles = [
  { value: 'weekly', label: 'Weekly', description: 'Billed every week' },
  { value: 'monthly', label: 'Monthly', description: 'Billed every month' },
  { value: 'quarterly', label: 'Quarterly', description: 'Billed every 3 months' },
  { value: 'yearly', label: 'Yearly', description: 'Billed every year' },
];

// Common subscription categories
const categories = [
  'Software & Tools',
  'Marketing & Advertising',
  'Communication',
  'Design & Creative',
  'Productivity',
  'Security',
  'Analytics',
  'Storage & Backup',
  'Entertainment',
  'Education',
  'Finance',
  'Other',
];

export default function NewSubscriptionPage() {
  const router = useRouter();
  const { business: businessParam } = router.query;
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState('USD');

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
  } = useForm<SubscriptionFormData>({
    resolver: zodResolver(subscriptionSchema),
    defaultValues: {
      name: '',
      description: '',
      amount: 0,
      currency: 'USD',
      billing_cycle: 'monthly',
      billing_date: '',
      business_id: '',
      category: '',
      website: '',
      notes: '',
      auto_renew: true,
      reminder_days: 3,
    },
  });

  // Watch form values
  const watchedAmount = watch('amount');
  const watchedCurrency = watch('currency');
  const watchedBillingCycle = watch('billing_cycle');

  // Fetch businesses
  const { data: businesses = [], isLoading: businessesLoading } = useQuery({
    queryKey: ['businesses'],
    queryFn: () => api.businesses.getAll(),
  });

  // Set default business if provided in URL
  useEffect(() => {
    if (businessParam && businesses.length > 0) {
      const business = businesses.find((b: any) => b.id === businessParam);
      if (business) {
        setValue('business_id', business.id);
        setValue('currency', business.currency || 'USD');
        setSelectedCurrency(business.currency || 'USD');
      }
    }
  }, [businessParam, businesses, setValue]);

  // Update selected currency when form currency changes
  useEffect(() => {
    setSelectedCurrency(watchedCurrency);
  }, [watchedCurrency]);

  // Set default billing date based on current date
  useEffect(() => {
    if (!watch('billing_date')) {
      const today = new Date();
      const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
      setValue('billing_date', nextMonth.toISOString().split('T')[0]);
    }
  }, [setValue, watch]);

  // Create subscription mutation
  const createMutation = useMutation({
    mutationFn: (data: SubscriptionFormData) => api.subscriptions.create(data),
    onSuccess: (subscription: any) => {
      queryClient.invalidateQueries({ queryKey: ['subscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Subscription created successfully!');
      router.push(`/subscriptions/${subscription.id}`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create subscription');
      setIsSubmitting(false);
    },
  });

  const onSubmit = async (data: SubscriptionFormData) => {
    setIsSubmitting(true);
    
    // Clean up empty strings
    const cleanedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === 'string' && value.trim() === '' ? undefined : value,
      ])
    ) as SubscriptionFormData;

    createMutation.mutate(cleanedData);
  };

  // Calculate next billing dates for preview
  const getNextBillingDates = () => {
    const billingDate = watch('billing_date');
    if (!billingDate) return [];

    const startDate = new Date(billingDate);
    const dates = [];
    
    for (let i = 0; i < 3; i++) {
      const date = new Date(startDate);
      
      switch (watchedBillingCycle) {
        case 'weekly':
          date.setDate(startDate.getDate() + (i * 7));
          break;
        case 'monthly':
          date.setMonth(startDate.getMonth() + i);
          break;
        case 'quarterly':
          date.setMonth(startDate.getMonth() + (i * 3));
          break;
        case 'yearly':
          date.setFullYear(startDate.getFullYear() + i);
          break;
      }
      
      dates.push(date.toLocaleDateString());
    }
    
    return dates;
  };

  if (businessesLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading..." />
        </div>
      </Layout>
    );
  }

  if (businesses.length === 0) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto text-center py-12">
          <BuildingOfficeIcon className="h-12 w-12 text-text-tertiary mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-text-primary mb-2">No Businesses Found</h1>
          <p className="text-text-secondary mb-6">
            You need to create a business before adding subscriptions.
          </p>
          <Link href="/businesses/new" className="btn-primary">
            Create Business
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>New Subscription - SubDash</title>
        <meta name="description" content="Add a new subscription" />
      </Head>

      <Layout>
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <Link
                href="/subscriptions"
                className="mr-4 p-2 rounded-md text-text-secondary hover:text-text-primary hover:bg-background-secondary transition-colors"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-text-primary">Add New Subscription</h1>
                <p className="text-sm text-text-secondary mt-1">
                  Track a new recurring subscription for your business
                </p>
              </div>
            </div>
          </div>

          {/* Form */}
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="card">
              <h2 className="text-lg font-medium text-text-primary mb-4 flex items-center">
                <CreditCardIcon className="h-5 w-5 mr-2" />
                Basic Information
              </h2>
              
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                {/* Business Selection */}
                <div>
                  <label htmlFor="business_id" className="block text-sm font-medium text-text-primary mb-1">
                    Business *
                  </label>
                  <select
                    {...register('business_id')}
                    className={`input-primary w-full ${
                      errors.business_id ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                    }`}
                  >
                    <option value="">Select a business</option>
                    {businesses.map((business: any) => (
                      <option key={business.id} value={business.id}>
                        {business.name}
                      </option>
                    ))}
                  </select>
                  {errors.business_id && (
                    <p className="mt-1 text-sm text-error-600">{errors.business_id.message}</p>
                  )}
                </div>

                {/* Subscription Name */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-text-primary mb-1">
                    Subscription Name *
                  </label>
                  <input
                    {...register('name')}
                    type="text"
                    className={`input-primary w-full ${
                      errors.name ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                    }`}
                    placeholder="e.g., Adobe Creative Cloud, Slack Pro"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-error-600">{errors.name.message}</p>
                  )}
                </div>

                {/* Description */}
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-text-primary mb-1">
                    Description
                  </label>
                  <textarea
                    {...register('description')}
                    rows={2}
                    className="input-primary w-full"
                    placeholder="Brief description of the subscription"
                  />
                </div>

                {/* Category */}
                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-text-primary mb-1">
                    Category
                  </label>
                  <select
                    {...register('category')}
                    className="input-primary w-full"
                  >
                    <option value="">Select a category</option>
                    {categories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Website */}
                <div>
                  <label htmlFor="website" className="block text-sm font-medium text-text-primary mb-1">
                    Website
                  </label>
                  <input
                    {...register('website')}
                    type="url"
                    className={`input-primary w-full ${
                      errors.website ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                    }`}
                    placeholder="https://example.com"
                  />
                  {errors.website && (
                    <p className="mt-1 text-sm text-error-600">{errors.website.message}</p>
                  )}
                </div>
              </form>
            </div>

            {/* Billing Information */}
            <div className="card">
              <h2 className="text-lg font-medium text-text-primary mb-4 flex items-center">
                <CurrencyDollarIcon className="h-5 w-5 mr-2" />
                Billing Information
              </h2>
              
              <div className="space-y-4">
                {/* Amount and Currency */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="amount" className="block text-sm font-medium text-text-primary mb-1">
                      Amount *
                    </label>
                    <input
                      {...register('amount', { valueAsNumber: true })}
                      type="number"
                      step="0.01"
                      min="0"
                      className={`input-primary w-full ${
                        errors.amount ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                      }`}
                      placeholder="0.00"
                    />
                    {errors.amount && (
                      <p className="mt-1 text-sm text-error-600">{errors.amount.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <label htmlFor="currency" className="block text-sm font-medium text-text-primary mb-1">
                      Currency *
                    </label>
                    <select
                      {...register('currency')}
                      className={`input-primary w-full ${
                        errors.currency ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                      }`}
                    >
                      {currencies.map((currency) => (
                        <option key={currency.code} value={currency.code}>
                          {currency.code} - {currency.name} ({currency.symbol})
                        </option>
                      ))}
                    </select>
                    {errors.currency && (
                      <p className="mt-1 text-sm text-error-600">{errors.currency.message}</p>
                    )}
                  </div>
                </div>

                {/* Amount Preview */}
                {watchedAmount > 0 && (
                  <div className="bg-background-secondary rounded-lg p-3">
                    <div className="text-sm text-text-secondary mb-1">Amount Preview</div>
                    <div className="text-lg font-semibold text-text-primary">
                      {formatCurrency(watchedAmount, selectedCurrency)}
                    </div>
                  </div>
                )}

                {/* Billing Cycle */}
                <div>
                  <label htmlFor="billing_cycle" className="block text-sm font-medium text-text-primary mb-1">
                    Billing Cycle *
                  </label>
                  <select
                    {...register('billing_cycle')}
                    className={`input-primary w-full ${
                      errors.billing_cycle ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                    }`}
                  >
                    {billingCycles.map((cycle) => (
                      <option key={cycle.value} value={cycle.value}>
                        {cycle.label} - {cycle.description}
                      </option>
                    ))}
                  </select>
                  {errors.billing_cycle && (
                    <p className="mt-1 text-sm text-error-600">{errors.billing_cycle.message}</p>
                  )}
                </div>

                {/* First Billing Date */}
                <div>
                  <label htmlFor="billing_date" className="block text-sm font-medium text-text-primary mb-1">
                    First Billing Date *
                  </label>
                  <input
                    {...register('billing_date')}
                    type="date"
                    className={`input-primary w-full ${
                      errors.billing_date ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                    }`}
                  />
                  {errors.billing_date && (
                    <p className="mt-1 text-sm text-error-600">{errors.billing_date.message}</p>
                  )}
                </div>

                {/* Billing Preview */}
                {watch('billing_date') && (
                  <div className="bg-background-secondary rounded-lg p-3">
                    <div className="text-sm text-text-secondary mb-2">Next Billing Dates</div>
                    <div className="space-y-1">
                      {getNextBillingDates().map((date, index) => (
                        <div key={index} className="flex items-center text-sm">
                          <CalendarIcon className="h-4 w-4 text-text-tertiary mr-2" />
                          <span className="text-text-primary">{date}</span>
                          {index === 0 && (
                            <span className="ml-2 text-xs text-primary-600 bg-primary-100 dark:bg-primary-900 px-2 py-0.5 rounded">
                              Next
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Additional Settings */}
            <div className="card">
              <h2 className="text-lg font-medium text-text-primary mb-4 flex items-center">
                <DocumentTextIcon className="h-5 w-5 mr-2" />
                Additional Settings
              </h2>
              
              <div className="space-y-4">
                {/* Auto Renew */}
                <div className="flex items-center">
                  <input
                    {...register('auto_renew')}
                    type="checkbox"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-border-primary rounded"
                  />
                  <label htmlFor="auto_renew" className="ml-2 block text-sm text-text-primary">
                    Auto-renew subscription
                  </label>
                </div>

                {/* Reminder Days */}
                <div>
                  <label htmlFor="reminder_days" className="block text-sm font-medium text-text-primary mb-1">
                    Reminder Days Before Billing
                  </label>
                  <select
                    {...register('reminder_days', { valueAsNumber: true })}
                    className="input-primary w-full"
                  >
                    <option value={0}>No reminder</option>
                    <option value={1}>1 day before</option>
                    <option value={3}>3 days before</option>
                    <option value={7}>1 week before</option>
                    <option value={14}>2 weeks before</option>
                    <option value={30}>1 month before</option>
                  </select>
                </div>

                {/* Notes */}
                <div>
                  <label htmlFor="notes" className="block text-sm font-medium text-text-primary mb-1">
                    Notes
                  </label>
                  <textarea
                    {...register('notes')}
                    rows={3}
                    className="input-primary w-full"
                    placeholder="Additional notes about this subscription"
                  />
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex flex-col sm:flex-row gap-3 pt-6">
              <Link
                href="/subscriptions"
                className="btn-secondary flex-1 text-center"
              >
                Cancel
              </Link>
              <button
                type="submit"
                onClick={handleSubmit(onSubmit)}
                disabled={isSubmitting}
                className="btn-primary flex-1 flex items-center justify-center"
              >
                {isSubmitting ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Creating...
                  </>
                ) : (
                  'Create Subscription'
                )}
              </button>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
}