{"name": "subscription-dashboard-frontend", "version": "1.0.0", "description": "Frontend for Subscription Dashboard - A comprehensive business subscription and payment management system", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "analyze": "cross-env ANALYZE=true next build", "clean": "rimraf .next out dist coverage"}, "dependencies": {"@supabase/supabase-js": "^2.38.4", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/auth-helpers-react": "^0.4.2", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@next/font": "^14.0.4", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "tailwind-merge": "^2.2.0", "lucide-react": "^0.294.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-query": "^3.39.3", "@tanstack/react-query": "^5.12.2", "@tanstack/react-query-devtools": "^5.13.3", "axios": "^1.6.2", "date-fns": "^2.30.0", "react-datepicker": "^4.25.0", "react-select": "^5.8.0", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "react-table": "^7.8.0", "@tanstack/react-table": "^8.10.7", "react-csv": "^2.2.2", "file-saver": "^2.0.5", "react-dropzone": "^14.2.3", "react-loading-skeleton": "^3.3.1", "framer-motion": "^10.16.16", "react-intersection-observer": "^9.5.3", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "currency.js": "^2.0.4", "numeral": "^2.0.6", "react-currency-input-field": "^3.6.11", "react-helmet-async": "^2.0.4", "next-themes": "^0.2.1", "js-cookie": "^3.0.5", "uuid": "^9.0.1", "lodash": "^4.17.21", "react-beautiful-dnd": "^13.1.1", "react-modal": "^3.16.1", "react-tooltip": "^5.25.0", "react-confetti": "^6.1.0", "react-use": "^17.4.2"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/js-cookie": "^3.0.6", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-modal": "^3.16.3", "@types/react-csv": "^1.1.10", "@types/file-saver": "^2.0.7", "@types/react-datepicker": "^4.19.4", "@types/react-table": "^7.7.18", "@types/react-window": "^1.8.8", "@types/numeral": "^2.0.5", "typescript": "^5.3.3", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "prettier": "^3.1.1", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/nextjs": "^7.6.6", "@storybook/react": "^7.6.6", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.6", "cross-env": "^7.0.3", "rimraf": "^5.0.5", "@next/bundle-analyzer": "^14.0.4", "webpack-bundle-analyzer": "^4.10.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "keywords": ["subscription", "dashboard", "payments", "business", "management", "nextjs", "react", "typescript", "supabase", "tailwindcss"], "author": "Subscription Dashboard Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/subscription-dashboard.git"}, "bugs": {"url": "https://github.com/your-org/subscription-dashboard/issues"}, "homepage": "https://github.com/your-org/subscription-dashboard#readme"}