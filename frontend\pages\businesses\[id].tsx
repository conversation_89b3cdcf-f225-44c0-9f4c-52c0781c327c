import { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { api } from '../../lib/api';
import { formatCurrency, formatDate } from '../../lib/utils';
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  BuildingOfficeIcon,
  GlobeAltIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  CreditCardIcon,
  DocumentTextIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import {
  CheckCircleIcon,
  XMarkIcon,
} from '@heroicons/react/24/solid';

// Validation schema
const businessSchema = z.object({
  name: z.string().min(2, 'Business name must be at least 2 characters'),
  description: z.string().optional(),
  industry: z.string().optional(),
  website: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  phone: z.string().optional(),
  email: z.string().email('Please enter a valid email').optional().or(z.literal('')),
  address: z.string().optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  currency: z.string().min(3, 'Please select a currency'),
  timezone: z.string().min(1, 'Please select a timezone'),
});

type BusinessFormData = z.infer<typeof businessSchema>;

// Common currencies
const currencies = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'KWD', name: 'Kuwaiti Dinar', symbol: 'د.ك' },
  { code: 'SAR', name: 'Saudi Riyal', symbol: 'ر.س' },
  { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ' },
  { code: 'QAR', name: 'Qatari Riyal', symbol: 'ر.ق' },
  { code: 'BHD', name: 'Bahraini Dinar', symbol: '.د.ب' },
  { code: 'OMR', name: 'Omani Rial', symbol: 'ر.ع.' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
];

// Common timezones
const timezones = [
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },
  { value: 'Europe/Paris', label: 'Central European Time (CET)' },
  { value: 'Asia/Kuwait', label: 'Kuwait Time (AST)' },
  { value: 'Asia/Riyadh', label: 'Arabia Standard Time (AST)' },
  { value: 'Asia/Dubai', label: 'Gulf Standard Time (GST)' },
  { value: 'Asia/Qatar', label: 'Arabia Standard Time (AST)' },
  { value: 'Asia/Bahrain', label: 'Arabia Standard Time (AST)' },
  { value: 'Asia/Muscat', label: 'Gulf Standard Time (GST)' },
  { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },
  { value: 'Asia/Shanghai', label: 'China Standard Time (CST)' },
  { value: 'Asia/Kolkata', label: 'India Standard Time (IST)' },
  { value: 'Australia/Sydney', label: 'Australian Eastern Time (AET)' },
];

// Common industries
const industries = [
  'Technology',
  'Healthcare',
  'Finance',
  'Education',
  'Retail',
  'Manufacturing',
  'Construction',
  'Real Estate',
  'Transportation',
  'Energy',
  'Media & Entertainment',
  'Food & Beverage',
  'Consulting',
  'Legal',
  'Non-profit',
  'Government',
  'Other',
];

export default function BusinessDetailPage() {
  const router = useRouter();
  const { id } = router.query;
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch business data
  const { data: business, isLoading, error } = useQuery({
    queryKey: ['business', id],
    queryFn: async () => {
      const response = await api.businesses.getById(id as string);
      return response.data;
    },
    enabled: !!id,
  });

  // Fetch business stats
  const { data: stats } = useQuery({
    queryKey: ['business-stats', id],
    queryFn: async () => {
      const response = await api.businesses.getStats(id as string);
      return response.data;
    },
    enabled: !!id,
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<BusinessFormData>({
    resolver: zodResolver(businessSchema),
  });

  // Reset form when business data loads
  useState(() => {
    if (business) {
      reset({
        name: business.name || '',
        description: business.description || '',
        industry: business.industry || '',
        website: business.website || '',
        phone: business.phone || '',
        email: business.email || '',
        address: business.address || '',
        city: business.city || '',
        country: business.country || '',
        currency: business.currency || 'USD',
        timezone: business.timezone || 'America/New_York',
      });
    }
  });

  // Update business mutation
  const updateMutation = useMutation({
    mutationFn: (data: BusinessFormData) => api.businesses.update(id as string, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['business', id] });
      queryClient.invalidateQueries({ queryKey: ['businesses'] });
      toast.success('Business updated successfully!');
      setIsEditing(false);
      setIsSubmitting(false);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update business');
      setIsSubmitting(false);
    },
  });

  // Delete business mutation
  const deleteMutation = useMutation({
    mutationFn: () => api.businesses.delete(id as string),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['businesses'] });
      toast.success('Business deleted successfully!');
      router.push('/businesses');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete business');
      setIsDeleting(false);
    },
  });

  const onSubmit = async (data: BusinessFormData) => {
    setIsSubmitting(true);
    
    // Clean up empty strings
    const cleanedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === 'string' && value.trim() === '' ? undefined : value,
      ])
    ) as BusinessFormData;

    updateMutation.mutate(cleanedData);
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    deleteMutation.mutate();
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    if (business) {
      reset({
        name: business.name || '',
        description: business.description || '',
        industry: business.industry || '',
        website: business.website || '',
        phone: business.phone || '',
        email: business.email || '',
        address: business.address || '',
        city: business.city || '',
        country: business.country || '',
        currency: business.currency || 'USD',
        timezone: business.timezone || 'America/New_York',
      });
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading business..." />
        </div>
      </Layout>
    );
  }

  if (error || !business) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto text-center py-12">
          <ExclamationTriangleIcon className="h-12 w-12 text-error-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-text-primary mb-2">Business Not Found</h1>
          <p className="text-text-secondary mb-6">
            The business you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <Link href="/businesses" className="btn-primary">
            Back to Businesses
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>{business.name} - SubDash</title>
        <meta name="description" content={`Manage ${business.name} subscriptions and expenses`} />
      </Head>

      <Layout>
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Link
                  href="/businesses"
                  className="mr-4 p-2 rounded-md text-text-secondary hover:text-text-primary hover:bg-background-secondary transition-colors"
                >
                  <ArrowLeftIcon className="h-5 w-5" />
                </Link>
                <div>
                  <h1 className="text-2xl font-bold text-text-primary">{business.name}</h1>
                  <p className="text-sm text-text-secondary mt-1">
                    Created {formatDate(business.created_at)}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                {!isEditing ? (
                  <>
                    <button
                      onClick={() => setIsEditing(true)}
                      className="btn-secondary flex items-center"
                    >
                      <PencilIcon className="h-4 w-4 mr-2" />
                      Edit
                    </button>
                    <button
                      onClick={() => setShowDeleteModal(true)}
                      className="btn-danger flex items-center"
                    >
                      <TrashIcon className="h-4 w-4 mr-2" />
                      Delete
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={handleCancelEdit}
                      className="btn-secondary"
                      disabled={isSubmitting}
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSubmit(onSubmit)}
                      disabled={isSubmitting}
                      className="btn-primary flex items-center"
                    >
                      {isSubmitting ? (
                        <>
                          <LoadingSpinner size="sm" className="mr-2" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <CheckCircleIcon className="h-4 w-4 mr-2" />
                          Save Changes
                        </>
                      )}
                    </button>
                  </>
                )}
              </div>
            </div>

            {/* Stats Cards */}
            {stats && (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="card">
                  <div className="flex items-center">
                    <div className="p-2 bg-primary-100 dark:bg-primary-900 rounded-lg">
                      <CreditCardIcon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-text-secondary">Active Subscriptions</p>
                      <p className="text-lg font-semibold text-text-primary">{stats.activeSubscriptions}</p>
                    </div>
                  </div>
                </div>
                <div className="card">
                  <div className="flex items-center">
                    <div className="p-2 bg-success-100 dark:bg-success-900 rounded-lg">
                      <ChartBarIcon className="h-5 w-5 text-success-600 dark:text-success-400" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-text-secondary">Monthly Spend</p>
                      <p className="text-lg font-semibold text-text-primary">
                        {formatCurrency(stats.monthlySpend, business.currency)}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="card">
                  <div className="flex items-center">
                    <div className="p-2 bg-warning-100 dark:bg-warning-900 rounded-lg">
                      <DocumentTextIcon className="h-5 w-5 text-warning-600 dark:text-warning-400" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-text-secondary">Total Payments</p>
                      <p className="text-lg font-semibold text-text-primary">{stats.totalPayments}</p>
                    </div>
                  </div>
                </div>
                <div className="card">
                  <div className="flex items-center">
                    <div className="p-2 bg-info-100 dark:bg-info-900 rounded-lg">
                      <DocumentTextIcon className="h-5 w-5 text-info-600 dark:text-info-400" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-text-secondary">Reimbursements</p>
                      <p className="text-lg font-semibold text-text-primary">{stats.pendingReimbursements}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Business Details Form */}
          <div className="card">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Basic Information */}
              <div>
                <h2 className="text-lg font-medium text-text-primary mb-4 flex items-center">
                  <BuildingOfficeIcon className="h-5 w-5 mr-2" />
                  Basic Information
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Business Name */}
                  <div className="md:col-span-2">
                    <label htmlFor="name" className="block text-sm font-medium text-text-primary mb-1">
                      Business Name *
                    </label>
                    {isEditing ? (
                      <input
                        {...register('name')}
                        type="text"
                        className={`input-primary w-full ${
                          errors.name ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                        }`}
                        placeholder="Enter business name"
                      />
                    ) : (
                      <p className="text-text-primary py-2">{business.name}</p>
                    )}
                    {errors.name && (
                      <p className="mt-1 text-sm text-error-600">{errors.name.message}</p>
                    )}
                  </div>

                  {/* Industry */}
                  <div>
                    <label htmlFor="industry" className="block text-sm font-medium text-text-primary mb-1">
                      Industry
                    </label>
                    {isEditing ? (
                      <select
                        {...register('industry')}
                        className="input-primary w-full"
                      >
                        <option value="">Select industry</option>
                        {industries.map((industry) => (
                          <option key={industry} value={industry}>
                            {industry}
                          </option>
                        ))}
                      </select>
                    ) : (
                      <p className="text-text-primary py-2">{business.industry || 'Not specified'}</p>
                    )}
                  </div>

                  {/* Currency */}
                  <div>
                    <label htmlFor="currency" className="block text-sm font-medium text-text-primary mb-1">
                      Currency *
                    </label>
                    {isEditing ? (
                      <select
                        {...register('currency')}
                        className={`input-primary w-full ${
                          errors.currency ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                        }`}
                      >
                        {currencies.map((currency) => (
                          <option key={currency.code} value={currency.code}>
                            {currency.code} - {currency.name} ({currency.symbol})
                          </option>
                        ))}
                      </select>
                    ) : (
                      <p className="text-text-primary py-2">
                        {currencies.find(c => c.code === business.currency)?.name || business.currency}
                      </p>
                    )}
                    {errors.currency && (
                      <p className="mt-1 text-sm text-error-600">{errors.currency.message}</p>
                    )}
                  </div>

                  {/* Timezone */}
                  <div className="md:col-span-2">
                    <label htmlFor="timezone" className="block text-sm font-medium text-text-primary mb-1">
                      Timezone *
                    </label>
                    {isEditing ? (
                      <select
                        {...register('timezone')}
                        className={`input-primary w-full ${
                          errors.timezone ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                        }`}
                      >
                        {timezones.map((timezone) => (
                          <option key={timezone.value} value={timezone.value}>
                            {timezone.label}
                          </option>
                        ))}
                      </select>
                    ) : (
                      <p className="text-text-primary py-2">
                        {timezones.find(t => t.value === business.timezone)?.label || business.timezone}
                      </p>
                    )}
                    {errors.timezone && (
                      <p className="mt-1 text-sm text-error-600">{errors.timezone.message}</p>
                    )}
                  </div>

                  {/* Description */}
                  <div className="md:col-span-2">
                    <label htmlFor="description" className="block text-sm font-medium text-text-primary mb-1">
                      Description
                    </label>
                    {isEditing ? (
                      <textarea
                        {...register('description')}
                        rows={3}
                        className="input-primary w-full"
                        placeholder="Brief description of your business"
                      />
                    ) : (
                      <p className="text-text-primary py-2">{business.description || 'No description provided'}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div>
                <h2 className="text-lg font-medium text-text-primary mb-4 flex items-center">
                  <EnvelopeIcon className="h-5 w-5 mr-2" />
                  Contact Information
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Website */}
                  <div>
                    <label htmlFor="website" className="block text-sm font-medium text-text-primary mb-1">
                      Website
                    </label>
                    {isEditing ? (
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <GlobeAltIcon className="h-4 w-4 text-text-tertiary" />
                        </div>
                        <input
                          {...register('website')}
                          type="url"
                          className={`input-primary w-full pl-10 ${
                            errors.website ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                          }`}
                          placeholder="https://example.com"
                        />
                      </div>
                    ) : (
                      <p className="text-text-primary py-2">
                        {business.website ? (
                          <a
                            href={business.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary-600 hover:text-primary-700 underline"
                          >
                            {business.website}
                          </a>
                        ) : (
                          'Not provided'
                        )}
                      </p>
                    )}
                    {errors.website && (
                      <p className="mt-1 text-sm text-error-600">{errors.website.message}</p>
                    )}
                  </div>

                  {/* Phone */}
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-text-primary mb-1">
                      Phone
                    </label>
                    {isEditing ? (
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <PhoneIcon className="h-4 w-4 text-text-tertiary" />
                        </div>
                        <input
                          {...register('phone')}
                          type="tel"
                          className="input-primary w-full pl-10"
                          placeholder="+****************"
                        />
                      </div>
                    ) : (
                      <p className="text-text-primary py-2">{business.phone || 'Not provided'}</p>
                    )}
                  </div>

                  {/* Email */}
                  <div className="md:col-span-2">
                    <label htmlFor="email" className="block text-sm font-medium text-text-primary mb-1">
                      Email
                    </label>
                    {isEditing ? (
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <EnvelopeIcon className="h-4 w-4 text-text-tertiary" />
                        </div>
                        <input
                          {...register('email')}
                          type="email"
                          className={`input-primary w-full pl-10 ${
                            errors.email ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                          }`}
                          placeholder="<EMAIL>"
                        />
                      </div>
                    ) : (
                      <p className="text-text-primary py-2">
                        {business.email ? (
                          <a
                            href={`mailto:${business.email}`}
                            className="text-primary-600 hover:text-primary-700 underline"
                          >
                            {business.email}
                          </a>
                        ) : (
                          'Not provided'
                        )}
                      </p>
                    )}
                    {errors.email && (
                      <p className="mt-1 text-sm text-error-600">{errors.email.message}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Address Information */}
              <div>
                <h2 className="text-lg font-medium text-text-primary mb-4 flex items-center">
                  <MapPinIcon className="h-5 w-5 mr-2" />
                  Address Information
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Address */}
                  <div className="md:col-span-2">
                    <label htmlFor="address" className="block text-sm font-medium text-text-primary mb-1">
                      Street Address
                    </label>
                    {isEditing ? (
                      <input
                        {...register('address')}
                        type="text"
                        className="input-primary w-full"
                        placeholder="123 Main Street"
                      />
                    ) : (
                      <p className="text-text-primary py-2">{business.address || 'Not provided'}</p>
                    )}
                  </div>

                  {/* City */}
                  <div>
                    <label htmlFor="city" className="block text-sm font-medium text-text-primary mb-1">
                      City
                    </label>
                    {isEditing ? (
                      <input
                        {...register('city')}
                        type="text"
                        className="input-primary w-full"
                        placeholder="New York"
                      />
                    ) : (
                      <p className="text-text-primary py-2">{business.city || 'Not provided'}</p>
                    )}
                  </div>

                  {/* Country */}
                  <div>
                    <label htmlFor="country" className="block text-sm font-medium text-text-primary mb-1">
                      Country
                    </label>
                    {isEditing ? (
                      <input
                        {...register('country')}
                        type="text"
                        className="input-primary w-full"
                        placeholder="United States"
                      />
                    ) : (
                      <p className="text-text-primary py-2">{business.country || 'Not provided'}</p>
                    )}
                  </div>
                </div>
              </div>
            </form>
          </div>

          {/* Quick Actions */}
          <div className="mt-8">
            <h2 className="text-lg font-medium text-text-primary mb-4">Quick Actions</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link
                href={`/subscriptions?business=${business.id}`}
                className="card hover:shadow-md transition-shadow p-4 text-center"
              >
                <CreditCardIcon className="h-8 w-8 text-primary-600 mx-auto mb-2" />
                <h3 className="font-medium text-text-primary">View Subscriptions</h3>
                <p className="text-sm text-text-secondary">Manage business subscriptions</p>
              </Link>
              <Link
                href={`/payments?business=${business.id}`}
                className="card hover:shadow-md transition-shadow p-4 text-center"
              >
                <DocumentTextIcon className="h-8 w-8 text-success-600 mx-auto mb-2" />
                <h3 className="font-medium text-text-primary">View Payments</h3>
                <p className="text-sm text-text-secondary">Track payment history</p>
              </Link>
              <Link
                href={`/reports?business=${business.id}`}
                className="card hover:shadow-md transition-shadow p-4 text-center"
              >
                <ChartBarIcon className="h-8 w-8 text-info-600 mx-auto mb-2" />
                <h3 className="font-medium text-text-primary">View Reports</h3>
                <p className="text-sm text-text-secondary">Generate business reports</p>
              </Link>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-background-primary rounded-lg max-w-md w-full p-6">
              <div className="flex items-center mb-4">
                <ExclamationTriangleIcon className="h-6 w-6 text-error-500 mr-3" />
                <h3 className="text-lg font-medium text-text-primary">Delete Business</h3>
              </div>
              <p className="text-text-secondary mb-6">
                Are you sure you want to delete "{business.name}"? This action cannot be undone and will also delete all associated subscriptions, payments, and reimbursements.
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="btn-secondary"
                  disabled={isDeleting}
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="btn-danger flex items-center"
                >
                  {isDeleting ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <TrashIcon className="h-4 w-4 mr-2" />
                      Delete Business
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </Layout>
    </>
  );
}