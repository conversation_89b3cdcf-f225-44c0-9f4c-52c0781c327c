/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "__barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,BuildingOfficeIcon,ChartBarIcon,CogIcon,CreditCardIcon,HomeIcon,UserCircleIcon,XMarkIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*******************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,BuildingOfficeIcon,ChartBarIcon,CogIcon,CreditCardIcon,HomeIcon,UserCircleIcon,XMarkIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowPathIcon: () => (/* reexport safe */ _ArrowPathIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ArrowRightOnRectangleIcon: () => (/* reexport safe */ _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   BanknotesIcon: () => (/* reexport safe */ _BanknotesIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   BuildingOfficeIcon: () => (/* reexport safe */ _BuildingOfficeIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   ChartBarIcon: () => (/* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   CogIcon: () => (/* reexport safe */ _CogIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   CreditCardIcon: () => (/* reexport safe */ _CreditCardIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   HomeIcon: () => (/* reexport safe */ _HomeIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   UserCircleIcon: () => (/* reexport safe */ _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   XMarkIcon: () => (/* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowPathIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowPathIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ArrowRightOnRectangleIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _BanknotesIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BanknotesIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/BanknotesIcon.js\");\n/* harmony import */ var _BuildingOfficeIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./BuildingOfficeIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/BuildingOfficeIcon.js\");\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _CogIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CogIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _CreditCardIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CreditCardIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _HomeIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./HomeIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./UserCircleIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./XMarkIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1BhdGhJY29uLEFycm93UmlnaHRPblJlY3RhbmdsZUljb24sQmFua25vdGVzSWNvbixCdWlsZGluZ09mZmljZUljb24sQ2hhcnRCYXJJY29uLENvZ0ljb24sQ3JlZGl0Q2FyZEljb24sSG9tZUljb24sVXNlckNpcmNsZUljb24sWE1hcmtJY29uIT0hLi4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQzZEO0FBQ3dCO0FBQ3hCO0FBQ1U7QUFDWjtBQUNWO0FBQ2M7QUFDWjtBQUNZIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3Vic2NyaXB0aW9uLWRhc2hib2FyZC1mcm9udGVuZC8uLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz9iOTk3Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcnJvd1BhdGhJY29uIH0gZnJvbSBcIi4vQXJyb3dQYXRoSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEFycm93UmlnaHRPblJlY3RhbmdsZUljb24gfSBmcm9tIFwiLi9BcnJvd1JpZ2h0T25SZWN0YW5nbGVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQmFua25vdGVzSWNvbiB9IGZyb20gXCIuL0Jhbmtub3Rlc0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCdWlsZGluZ09mZmljZUljb24gfSBmcm9tIFwiLi9CdWlsZGluZ09mZmljZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGFydEJhckljb24gfSBmcm9tIFwiLi9DaGFydEJhckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb2dJY29uIH0gZnJvbSBcIi4vQ29nSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENyZWRpdENhcmRJY29uIH0gZnJvbSBcIi4vQ3JlZGl0Q2FyZEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIb21lSWNvbiB9IGZyb20gXCIuL0hvbWVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlckNpcmNsZUljb24gfSBmcm9tIFwiLi9Vc2VyQ2lyY2xlSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFhNYXJrSWNvbiB9IGZyb20gXCIuL1hNYXJrSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,BuildingOfficeIcon,ChartBarIcon,CogIcon,CreditCardIcon,HomeIcon,UserCircleIcon,XMarkIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ArrowPathIcon,BanknotesIcon,BuildingOfficeIcon,ChartBarIcon,CogIcon,CreditCardIcon,HomeIcon!=!../node_modules/@heroicons/react/24/solid/esm/index.js":
/*!**************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowPathIcon,BanknotesIcon,BuildingOfficeIcon,ChartBarIcon,CogIcon,CreditCardIcon,HomeIcon!=!../node_modules/@heroicons/react/24/solid/esm/index.js ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowPathIcon: () => (/* reexport safe */ _ArrowPathIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   BanknotesIcon: () => (/* reexport safe */ _BanknotesIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   BuildingOfficeIcon: () => (/* reexport safe */ _BuildingOfficeIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   ChartBarIcon: () => (/* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   CogIcon: () => (/* reexport safe */ _CogIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   CreditCardIcon: () => (/* reexport safe */ _CreditCardIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   HomeIcon: () => (/* reexport safe */ _HomeIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowPathIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowPathIcon.js */ \"../node_modules/@heroicons/react/24/solid/esm/ArrowPathIcon.js\");\n/* harmony import */ var _BanknotesIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BanknotesIcon.js */ \"../node_modules/@heroicons/react/24/solid/esm/BanknotesIcon.js\");\n/* harmony import */ var _BuildingOfficeIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BuildingOfficeIcon.js */ \"../node_modules/@heroicons/react/24/solid/esm/BuildingOfficeIcon.js\");\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"../node_modules/@heroicons/react/24/solid/esm/ChartBarIcon.js\");\n/* harmony import */ var _CogIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CogIcon.js */ \"../node_modules/@heroicons/react/24/solid/esm/CogIcon.js\");\n/* harmony import */ var _CreditCardIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CreditCardIcon.js */ \"../node_modules/@heroicons/react/24/solid/esm/CreditCardIcon.js\");\n/* harmony import */ var _HomeIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./HomeIcon.js */ \"../node_modules/@heroicons/react/24/solid/esm/HomeIcon.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1BhdGhJY29uLEJhbmtub3Rlc0ljb24sQnVpbGRpbmdPZmZpY2VJY29uLENoYXJ0QmFySWNvbixDb2dJY29uLENyZWRpdENhcmRJY29uLEhvbWVJY29uIT0hLi4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvc29saWQvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUM2RDtBQUNBO0FBQ1U7QUFDWjtBQUNWO0FBQ2MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdWJzY3JpcHRpb24tZGFzaGJvYXJkLWZyb250ZW5kLy4uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L3NvbGlkL2VzbS9pbmRleC5qcz8wZWQ1Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcnJvd1BhdGhJY29uIH0gZnJvbSBcIi4vQXJyb3dQYXRoSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJhbmtub3Rlc0ljb24gfSBmcm9tIFwiLi9CYW5rbm90ZXNJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQnVpbGRpbmdPZmZpY2VJY29uIH0gZnJvbSBcIi4vQnVpbGRpbmdPZmZpY2VJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hhcnRCYXJJY29uIH0gZnJvbSBcIi4vQ2hhcnRCYXJJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ29nSWNvbiB9IGZyb20gXCIuL0NvZ0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDcmVkaXRDYXJkSWNvbiB9IGZyb20gXCIuL0NyZWRpdENhcmRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSG9tZUljb24gfSBmcm9tIFwiLi9Ib21lSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowPathIcon,BanknotesIcon,BuildingOfficeIcon,ChartBarIcon,CogIcon,CreditCardIcon,HomeIcon!=!../node_modules/@heroicons/react/24/solid/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,MagnifyingGlassIcon,MoonIcon,SunIcon,UserCircleIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js":
/*!***************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,MagnifyingGlassIcon,MoonIcon,SunIcon,UserCircleIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \***************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRightOnRectangleIcon: () => (/* reexport safe */ _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Bars3Icon: () => (/* reexport safe */ _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   BellIcon: () => (/* reexport safe */ _BellIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   ChevronDownIcon: () => (/* reexport safe */ _ChevronDownIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Cog6ToothIcon: () => (/* reexport safe */ _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   MagnifyingGlassIcon: () => (/* reexport safe */ _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   MoonIcon: () => (/* reexport safe */ _MoonIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   SunIcon: () => (/* reexport safe */ _SunIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   UserCircleIcon: () => (/* reexport safe */ _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowRightOnRectangleIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Bars3Icon.js */ \"../node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _BellIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BellIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _ChevronDownIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ChevronDownIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Cog6ToothIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MagnifyingGlassIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _MoonIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./MoonIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/MoonIcon.js\");\n/* harmony import */ var _SunIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./SunIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/SunIcon.js\");\n/* harmony import */ var _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./UserCircleIcon.js */ \"../node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1JpZ2h0T25SZWN0YW5nbGVJY29uLEJhcnMzSWNvbixCZWxsSWNvbixDaGV2cm9uRG93bkljb24sQ29nNlRvb3RoSWNvbixNYWduaWZ5aW5nR2xhc3NJY29uLE1vb25JY29uLFN1bkljb24sVXNlckNpcmNsZUljb24hPSEuLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3FGO0FBQ2hDO0FBQ0Y7QUFDYztBQUNKO0FBQ1k7QUFDdEI7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3N1YnNjcmlwdGlvbi1kYXNoYm9hcmQtZnJvbnRlbmQvLi4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/NjZkNiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJyb3dSaWdodE9uUmVjdGFuZ2xlSWNvbiB9IGZyb20gXCIuL0Fycm93UmlnaHRPblJlY3RhbmdsZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCYXJzM0ljb24gfSBmcm9tIFwiLi9CYXJzM0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCZWxsSWNvbiB9IGZyb20gXCIuL0JlbGxJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hldnJvbkRvd25JY29uIH0gZnJvbSBcIi4vQ2hldnJvbkRvd25JY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ29nNlRvb3RoSWNvbiB9IGZyb20gXCIuL0NvZzZUb290aEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNYWduaWZ5aW5nR2xhc3NJY29uIH0gZnJvbSBcIi4vTWFnbmlmeWluZ0dsYXNzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1vb25JY29uIH0gZnJvbSBcIi4vTW9vbkljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTdW5JY29uIH0gZnJvbSBcIi4vU3VuSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXJDaXJjbGVJY29uIH0gZnJvbSBcIi4vVXNlckNpcmNsZUljb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,MagnifyingGlassIcon,MoonIcon,SunIcon,UserCircleIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! private-next-pages/_error */ \"../node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__]);\nprivate_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/layout/Header.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useAuth */ \"./hooks/useAuth.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,MagnifyingGlassIcon,MoonIcon,SunIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,ChevronDownIcon,Cog6ToothIcon,MagnifyingGlassIcon,MoonIcon,SunIcon,UserCircleIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _hooks_useTheme__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/useTheme */ \"./hooks/useTheme.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\n\nconst Header = ({ onMenuClick, showMenuButton, title, description })=>{\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showUserMenu, setShowUserMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const userMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const notificationsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, signOut } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { theme, toggleTheme } = (0,_hooks_useTheme__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    // Generate breadcrumbs from current route\n    const generateBreadcrumbs = ()=>{\n        const pathSegments = router.pathname.split(\"/\").filter(Boolean);\n        const breadcrumbs = [\n            {\n                name: \"Dashboard\",\n                href: \"/dashboard\"\n            }\n        ];\n        let currentPath = \"\";\n        pathSegments.forEach((segment, index)=>{\n            currentPath += `/${segment}`;\n            // Skip if it's the dashboard route\n            if (segment === \"dashboard\") return;\n            // Format segment name\n            let name = segment.charAt(0).toUpperCase() + segment.slice(1);\n            // Handle special cases\n            if (segment === \"auth\") name = \"Authentication\";\n            if (segment === \"reimbursements\") name = \"Reimbursements\";\n            // Don't add href for the last segment (current page)\n            const href = index === pathSegments.length - 1 ? undefined : currentPath;\n            breadcrumbs.push({\n                name,\n                href\n            });\n        });\n        return breadcrumbs;\n    };\n    const breadcrumbs = generateBreadcrumbs();\n    // Close dropdowns when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {\n                setShowUserMenu(false);\n            }\n            if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {\n                setShowNotifications(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n    }, []);\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            router.push(\"/auth/signin\");\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n        }\n    };\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (searchQuery.trim()) {\n            // Implement search functionality\n            console.log(\"Searching for:\", searchQuery);\n        }\n    };\n    // Mock notifications data\n    const notifications = [\n        {\n            id: 1,\n            title: \"Payment Due\",\n            message: \"Netflix subscription payment is due tomorrow\",\n            time: \"2 hours ago\",\n            unread: true\n        },\n        {\n            id: 2,\n            title: \"New Business Added\",\n            message: \"Tech Solutions LLC has been successfully created\",\n            time: \"1 day ago\",\n            unread: false\n        },\n        {\n            id: 3,\n            title: \"Reimbursement Approved\",\n            message: \"Your office supplies reimbursement has been approved\",\n            time: \"2 days ago\",\n            unread: false\n        }\n    ];\n    const unreadCount = notifications.filter((n)=>n.unread).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"px-4 lg:px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                showMenuButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onMenuClick,\n                                    className: \"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors lg:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.Bars3Icon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex\",\n                                    \"aria-label\": \"Breadcrumb\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: breadcrumbs.map((breadcrumb, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-4 w-4 text-gray-300 dark:text-gray-600 mx-2\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    breadcrumb.href ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                        href: breadcrumb.href,\n                                                        className: \"text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors\",\n                                                        children: breadcrumb.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 23\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                        children: breadcrumb.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, breadcrumb.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 max-w-lg mx-4 hidden md:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSearch,\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.MagnifyingGlassIcon, {\n                                            className: \"h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        placeholder: \"Search...\",\n                                        className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent text-sm transition-colors\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleTheme,\n                                    className: \"p-2 rounded-lg text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\",\n                                    title: `Switch to ${theme === \"dark\" ? \"light\" : \"dark\"} mode`,\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.SunIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.MoonIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    ref: notificationsRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowNotifications(!showNotifications),\n                                            className: \"p-2 rounded-lg text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.BellIcon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                                    children: unreadCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        showNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-4 py-2 border-b border-gray-200 dark:border-gray-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                        children: \"Notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-64 overflow-y-auto\",\n                                                    children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${notification.unread ? \"bg-blue-50 dark:bg-blue-900/20\" : \"\"}`,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 min-w-0\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                                                children: notification.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                                                lineNumber: 249,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500 dark:text-gray-400 mt-1\",\n                                                                                children: notification.message\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                                                lineNumber: 252,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-400 dark:text-gray-500 mt-1\",\n                                                                                children: notification.time\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                                                lineNumber: 255,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    notification.unread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-2 w-2 bg-blue-500 rounded-full flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, notification.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-4 py-2 border-t border-gray-200 dark:border-gray-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-sm text-brand-600 dark:text-brand-400 hover:text-brand-700 dark:hover:text-brand-300 font-medium\",\n                                                        children: \"View all notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    ref: userMenuRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowUserMenu(!showUserMenu),\n                                            className: \"flex items-center space-x-2 p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 w-6 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center\",\n                                                    children: user?.user_metadata?.avatar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: user.user_metadata.avatar_url,\n                                                        alt: \"Profile\",\n                                                        className: \"h-6 w-6 rounded-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.UserCircleIcon, {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ChevronDownIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        showUserMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-4 py-2 border-b border-gray-200 dark:border-gray-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                            children: user?.user_metadata?.full_name || user?.email?.split(\"@\")[0] || \"User\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: user?.email\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/settings\",\n                                                    className: \"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                    onClick: ()=>setShowUserMenu(false),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.Cog6ToothIcon, {\n                                                            className: \"mr-3 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Settings\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleSignOut,\n                                                    className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_ChevronDownIcon_Cog6ToothIcon_MagnifyingGlassIcon_MoonIcon_SunIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ArrowRightOnRectangleIcon, {\n                                                            className: \"mr-3 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Sign out\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, undefined),\n                (title || description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pb-4\",\n                    children: [\n                        title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 15\n                        }, undefined),\n                        description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Header.tsx\n");

/***/ }),

/***/ "./components/layout/Layout.tsx":
/*!**************************************!*\
  !*** ./components/layout/Layout.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useAuth */ \"./hooks/useAuth.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"./components/layout/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"./components/layout/Header.tsx\");\n/* harmony import */ var _ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ui/LoadingSpinner */ \"./components/ui/LoadingSpinner.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__]);\n_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\nconst Layout = ({ children, title, description })=>{\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check if current route requires authentication\n    const publicRoutes = [\n        \"/auth/signin\",\n        \"/auth/signup\",\n        \"/auth/forgot-password\",\n        \"/\"\n    ];\n    const isPublicRoute = publicRoutes.includes(router.pathname);\n    // Handle responsive sidebar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            const mobile = window.innerWidth < 1024;\n            setIsMobile(mobile);\n            if (!mobile) {\n                setSidebarOpen(false);\n            }\n        };\n        handleResize();\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    // Redirect to signin if not authenticated and not on public route\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !user && !isPublicRoute) {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        user,\n        loading,\n        isPublicRoute,\n        router\n    ]);\n    // Show loading spinner while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-background\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                size: \"lg\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Layout.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Render public pages without layout\n    if (isPublicRoute) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Layout.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Redirect to signin if not authenticated\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: [\n            sidebarOpen && isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                open: sidebarOpen,\n                onClose: ()=>setSidebarOpen(false),\n                isMobile: isMobile\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex flex-col ${isMobile ? \"\" : \"lg:pl-64\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        onMenuClick: ()=>setSidebarOpen(true),\n                        showMenuButton: isMobile,\n                        title: title,\n                        description: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 lg:p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-7xl\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Layout.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Layout.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Layout.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Layout.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Layout.tsx\n");

/***/ }),

/***/ "./components/layout/Sidebar.tsx":
/*!***************************************!*\
  !*** ./components/layout/Sidebar.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/useAuth */ \"./hooks/useAuth.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,BuildingOfficeIcon,ChartBarIcon,CogIcon,CreditCardIcon,HomeIcon,UserCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowPathIcon,ArrowRightOnRectangleIcon,BanknotesIcon,BuildingOfficeIcon,ChartBarIcon,CogIcon,CreditCardIcon,HomeIcon,UserCircleIcon,XMarkIcon!=!../node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BanknotesIcon,BuildingOfficeIcon,ChartBarIcon,CogIcon,CreditCardIcon,HomeIcon!=!@heroicons/react/24/solid */ \"__barrel_optimize__?names=ArrowPathIcon,BanknotesIcon,BuildingOfficeIcon,ChartBarIcon,CogIcon,CreditCardIcon,HomeIcon!=!../node_modules/@heroicons/react/24/solid/esm/index.js\");\n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.HomeIcon,\n        iconSolid: _barrel_optimize_names_ArrowPathIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_6__.HomeIcon\n    },\n    {\n        name: \"Businesses\",\n        href: \"/businesses\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.BuildingOfficeIcon,\n        iconSolid: _barrel_optimize_names_ArrowPathIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_6__.BuildingOfficeIcon\n    },\n    {\n        name: \"Subscriptions\",\n        href: \"/subscriptions\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ArrowPathIcon,\n        iconSolid: _barrel_optimize_names_ArrowPathIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_6__.ArrowPathIcon\n    },\n    {\n        name: \"Payments\",\n        href: \"/payments\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CreditCardIcon,\n        iconSolid: _barrel_optimize_names_ArrowPathIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_6__.CreditCardIcon\n    },\n    {\n        name: \"Reimbursements\",\n        href: \"/reimbursements\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.BanknotesIcon,\n        iconSolid: _barrel_optimize_names_ArrowPathIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_6__.BanknotesIcon\n    },\n    {\n        name: \"Reports\",\n        href: \"/reports\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ChartBarIcon,\n        iconSolid: _barrel_optimize_names_ArrowPathIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_6__.ChartBarIcon\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CogIcon,\n        iconSolid: _barrel_optimize_names_ArrowPathIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_6__.CogIcon\n    }\n];\nconst Sidebar = ({ open, onClose, isMobile })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { user, signOut } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            router.push(\"/auth/signin\");\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n        }\n    };\n    const isActive = (href)=>{\n        if (href === \"/dashboard\") {\n            return router.pathname === \"/dashboard\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    const sidebarClasses = `\n    fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 transform transition-transform duration-300 ease-in-out\n    ${isMobile ? open ? \"translate-x-0\" : \"-translate-x-full\" : \"translate-x-0\"}\n    ${!isMobile ? \"lg:translate-x-0\" : \"\"}\n  `;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: sidebarClasses,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-full flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 items-center justify-between px-4 border-b border-gray-200 dark:border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-lg bg-gradient-to-br from-brand-500 to-brand-600 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"SD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                    children: \"SubDash\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, undefined),\n                        isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.XMarkIcon, {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex-1 px-4 py-6 space-y-1 overflow-y-auto\",\n                    children: navigation.map((item)=>{\n                        const active = isActive(item.href);\n                        const Icon = active ? item.iconSolid : item.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: item.href,\n                            onClick: ()=>isMobile && onClose(),\n                            className: `\n                  group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200\n                  ${active ? \"bg-brand-50 text-brand-700 dark:bg-brand-900/20 dark:text-brand-400\" : \"text-gray-700 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white\"}\n                `,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: `\n                    mr-3 h-5 w-5 flex-shrink-0 transition-colors\n                    ${active ? \"text-brand-600 dark:text-brand-400\" : \"text-gray-400 group-hover:text-gray-500 dark:text-gray-400 dark:group-hover:text-gray-300\"}\n                  `\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex-1\",\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-brand-100 text-brand-800 dark:bg-brand-900/30 dark:text-brand-400\",\n                                    children: item.badge\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, item.name, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-200 dark:border-gray-700 p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center\",\n                                    children: user?.user_metadata?.avatar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: user.user_metadata.avatar_url,\n                                        alt: \"Profile\",\n                                        className: \"h-8 w-8 rounded-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.UserCircleIcon, {\n                                        className: \"h-5 w-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900 dark:text-white truncate\",\n                                            children: user?.user_metadata?.full_name || user?.email?.split(\"@\")[0] || \"User\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                            children: user?.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSignOut,\n                            className: \"w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white rounded-lg transition-all duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ArrowRightOnRectangleIcon_BanknotesIcon_BuildingOfficeIcon_ChartBarIcon_CogIcon_CreditCardIcon_HomeIcon_UserCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ArrowRightOnRectangleIcon, {\n                                    className: \"mr-3 h-5 w-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Sign out\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "./components/ui/LoadingSpinner.tsx":
/*!******************************************!*\
  !*** ./components/ui/LoadingSpinner.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/utils */ \"./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst sizeClasses = {\n    sm: \"h-4 w-4\",\n    md: \"h-6 w-6\",\n    lg: \"h-8 w-8\",\n    xl: \"h-12 w-12\"\n};\nconst colorClasses = {\n    primary: \"text-brand-600\",\n    secondary: \"text-gray-600\",\n    white: \"text-white\",\n    gray: \"text-gray-400\"\n};\nconst LoadingSpinner = ({ size = \"md\", className, color = \"primary\", text })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col items-center justify-center\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"animate-spin\", sizeClasses[size], colorClasses[color]),\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined),\n            text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-2 text-sm font-medium\", color === \"white\" ? \"text-white\" : \"text-gray-600 dark:text-gray-400\"),\n                children: text\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingSpinner);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ui/LoadingSpinner.tsx\n");

/***/ }),

/***/ "./hooks/useAuth.tsx":
/*!***************************!*\
  !*** ./hooks/useAuth.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useGuestOnly: () => (/* binding */ useGuestOnly),\n/* harmony export */   useRequireAuth: () => (/* binding */ useRequireAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"./lib/supabase.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial session\n        const getInitialSession = async ()=>{\n            try {\n                const { data: { session }, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n                if (error) {\n                    console.error(\"Error getting session:\", error);\n                } else {\n                    setSession(session);\n                    setUser(session?.user ?? null);\n                }\n            } catch (error) {\n                console.error(\"Error in getInitialSession:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log(\"Auth state changed:\", event, session?.user?.email);\n            setSession(session);\n            setUser(session?.user ?? null);\n            setLoading(false);\n            // Handle different auth events\n            switch(event){\n                case \"SIGNED_IN\":\n                    // Redirect to dashboard after successful sign in\n                    if (router.pathname.startsWith(\"/auth/\")) {\n                        router.push(\"/dashboard\");\n                    }\n                    break;\n                case \"SIGNED_OUT\":\n                    // Redirect to sign in page after sign out\n                    router.push(\"/auth/signin\");\n                    break;\n                case \"PASSWORD_RECOVERY\":\n                    // Redirect to reset password page\n                    router.push(\"/auth/reset-password\");\n                    break;\n                case \"TOKEN_REFRESHED\":\n                    console.log(\"Token refreshed successfully\");\n                    break;\n                case \"USER_UPDATED\":\n                    console.log(\"User profile updated\");\n                    break;\n            }\n        });\n        return ()=>{\n            subscription.unsubscribe();\n        };\n    }, [\n        router\n    ]);\n    const signUp = async (email, password, metadata)=>{\n        try {\n            setLoading(true);\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: metadata\n                }\n            });\n            if (error) {\n                console.error(\"Sign up error:\", error);\n                return {\n                    user: null,\n                    error\n                };\n            }\n            return {\n                user: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"Sign up error:\", error);\n            return {\n                user: null,\n                error: error\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signIn = async (email, password)=>{\n        try {\n            setLoading(true);\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                console.error(\"Sign in error:\", error);\n                return {\n                    user: null,\n                    error\n                };\n            }\n            return {\n                user: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"Sign in error:\", error);\n            return {\n                user: null,\n                error: error\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            setLoading(true);\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n            if (error) {\n                console.error(\"Sign out error:\", error);\n                return {\n                    error\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            console.error(\"Sign out error:\", error);\n            return {\n                error: error\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Alias for signOut to match interface\n    const logout = async ()=>{\n        return signOut();\n    };\n    const resetPassword = async (email)=>{\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.resetPasswordForEmail(email, {\n                redirectTo: `${window.location.origin}/auth/reset-password`\n            });\n            if (error) {\n                console.error(\"Reset password error:\", error);\n                return {\n                    error\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            console.error(\"Reset password error:\", error);\n            return {\n                error: error\n            };\n        }\n    };\n    const updateProfile = async (updates)=>{\n        try {\n            setLoading(true);\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.updateUser({\n                data: updates\n            });\n            if (error) {\n                console.error(\"Update profile error:\", error);\n                return {\n                    user: null,\n                    error\n                };\n            }\n            return {\n                user: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"Update profile error:\", error);\n            return {\n                user: null,\n                error: error\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updatePassword = async (password)=>{\n        try {\n            setLoading(true);\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.updateUser({\n                password\n            });\n            if (error) {\n                console.error(\"Update password error:\", error);\n                return {\n                    user: null,\n                    error\n                };\n            }\n            return {\n                user: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"Update password error:\", error);\n            return {\n                user: null,\n                error: error\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signOut,\n        resetPassword,\n        updateProfile,\n        updatePassword\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 232,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// Hook for protected routes\nfunction useRequireAuth() {\n    const { user, loading } = useAuth();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !user) {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        user,\n        loading,\n        router\n    ]);\n    return {\n        user,\n        loading\n    };\n}\n// Hook for guest routes (redirect if authenticated)\nfunction useGuestOnly() {\n    const { user, loading } = useAuth();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && user) {\n            router.push(\"/dashboard\");\n        }\n    }, [\n        user,\n        loading,\n        router\n    ]);\n    return {\n        user,\n        loading\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./hooks/useAuth.tsx\n");

/***/ }),

/***/ "./hooks/useTheme.tsx":
/*!****************************!*\
  !*** ./hooks/useTheme.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   usePrefersReducedMotion: () => (/* binding */ usePrefersReducedMotion),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useThemeColors: () => (/* binding */ useThemeColors),\n/* harmony export */   useThemeMediaQuery: () => (/* binding */ useThemeMediaQuery)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst THEME_STORAGE_KEY = \"subdash-theme\";\nfunction ThemeProvider({ children }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"system\");\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get system theme preference\n    const getSystemTheme = ()=>{\n        if (true) return \"light\";\n        return window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n    };\n    // Resolve theme based on current theme setting\n    const resolveTheme = (currentTheme)=>{\n        if (currentTheme === \"system\") {\n            return getSystemTheme();\n        }\n        return currentTheme;\n    };\n    // Apply theme to document\n    const applyTheme = (resolvedTheme)=>{\n        if (true) return;\n        const root = window.document.documentElement;\n        // Remove existing theme classes\n        root.classList.remove(\"light\", \"dark\");\n        // Add new theme class\n        root.classList.add(resolvedTheme);\n        // Update meta theme-color for mobile browsers\n        const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');\n        if (metaThemeColor) {\n            metaThemeColor.setAttribute(\"content\", resolvedTheme === \"dark\" ? \"#111827\" : \"#ffffff\");\n        }\n    };\n    // Load theme from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) return;\n        try {\n            const savedTheme = localStorage.getItem(THEME_STORAGE_KEY);\n            const initialTheme = savedTheme || \"system\";\n            setThemeState(initialTheme);\n            const resolved = resolveTheme(initialTheme);\n            setResolvedTheme(resolved);\n            applyTheme(resolved);\n        } catch (error) {\n            console.error(\"Error loading theme from localStorage:\", error);\n            // Fallback to system theme\n            const systemTheme = getSystemTheme();\n            setResolvedTheme(systemTheme);\n            applyTheme(systemTheme);\n        }\n        setMounted(true);\n    }, []);\n    // Listen for system theme changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) return;\n        const mediaQuery = window.matchMedia(\"(prefers-color-scheme: dark)\");\n        const handleChange = ()=>{\n            if (theme === \"system\") {\n                const newResolvedTheme = getSystemTheme();\n                setResolvedTheme(newResolvedTheme);\n                applyTheme(newResolvedTheme);\n            }\n        };\n        mediaQuery.addEventListener(\"change\", handleChange);\n        return ()=>{\n            mediaQuery.removeEventListener(\"change\", handleChange);\n        };\n    }, [\n        theme\n    ]);\n    // Update resolved theme when theme changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        const newResolvedTheme = resolveTheme(theme);\n        setResolvedTheme(newResolvedTheme);\n        applyTheme(newResolvedTheme);\n    }, [\n        theme,\n        mounted\n    ]);\n    const setTheme = (newTheme)=>{\n        try {\n            setThemeState(newTheme);\n            localStorage.setItem(THEME_STORAGE_KEY, newTheme);\n        } catch (error) {\n            console.error(\"Error saving theme to localStorage:\", error);\n        }\n    };\n    const toggleTheme = ()=>{\n        if (theme === \"light\") {\n            setTheme(\"dark\");\n        } else if (theme === \"dark\") {\n            setTheme(\"system\");\n        } else {\n            setTheme(\"light\");\n        }\n    };\n    const value = {\n        theme,\n        resolvedTheme,\n        setTheme,\n        toggleTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\hooks\\\\useTheme.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\n// Hook to get theme-aware colors\nfunction useThemeColors() {\n    const { resolvedTheme } = useTheme();\n    const colors = {\n        light: {\n            background: \"#ffffff\",\n            foreground: \"#0f172a\",\n            card: \"#ffffff\",\n            cardForeground: \"#0f172a\",\n            popover: \"#ffffff\",\n            popoverForeground: \"#0f172a\",\n            primary: \"#3b82f6\",\n            primaryForeground: \"#f8fafc\",\n            secondary: \"#f1f5f9\",\n            secondaryForeground: \"#0f172a\",\n            muted: \"#f1f5f9\",\n            mutedForeground: \"#64748b\",\n            accent: \"#f1f5f9\",\n            accentForeground: \"#0f172a\",\n            destructive: \"#ef4444\",\n            destructiveForeground: \"#f8fafc\",\n            border: \"#e2e8f0\",\n            input: \"#e2e8f0\",\n            ring: \"#3b82f6\"\n        },\n        dark: {\n            background: \"#0f172a\",\n            foreground: \"#f8fafc\",\n            card: \"#0f172a\",\n            cardForeground: \"#f8fafc\",\n            popover: \"#0f172a\",\n            popoverForeground: \"#f8fafc\",\n            primary: \"#60a5fa\",\n            primaryForeground: \"#1e293b\",\n            secondary: \"#1e293b\",\n            secondaryForeground: \"#f8fafc\",\n            muted: \"#1e293b\",\n            mutedForeground: \"#94a3b8\",\n            accent: \"#1e293b\",\n            accentForeground: \"#f8fafc\",\n            destructive: \"#7f1d1d\",\n            destructiveForeground: \"#f8fafc\",\n            border: \"#1e293b\",\n            input: \"#1e293b\",\n            ring: \"#1d4ed8\"\n        }\n    };\n    return colors[resolvedTheme];\n}\n// Hook for theme-aware media queries\nfunction useThemeMediaQuery() {\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) return;\n        const mediaQuery = window.matchMedia(\"(prefers-color-scheme: dark)\");\n        setMatches(mediaQuery.matches);\n        const handleChange = (e)=>{\n            setMatches(e.matches);\n        };\n        mediaQuery.addEventListener(\"change\", handleChange);\n        return ()=>{\n            mediaQuery.removeEventListener(\"change\", handleChange);\n        };\n    }, []);\n    return matches;\n}\n// Hook to detect if user prefers reduced motion\nfunction usePrefersReducedMotion() {\n    const [prefersReducedMotion, setPrefersReducedMotion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) return;\n        const mediaQuery = window.matchMedia(\"(prefers-reduced-motion: reduce)\");\n        setPrefersReducedMotion(mediaQuery.matches);\n        const handleChange = (e)=>{\n            setPrefersReducedMotion(e.matches);\n        };\n        mediaQuery.addEventListener(\"change\", handleChange);\n        return ()=>{\n            mediaQuery.removeEventListener(\"change\", handleChange);\n        };\n    }, []);\n    return prefersReducedMotion;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./hooks/useTheme.tsx\n");

/***/ }),

/***/ "./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dbHelpers: () => (/* binding */ dbHelpers),\n/* harmony export */   realtimeHelpers: () => (/* binding */ realtimeHelpers),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\nif (!supabaseUrl) {\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\nif (!supabaseAnonKey) {\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\n// Create Supabase client with additional configuration\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        // Configure auth settings\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true,\n        flowType: \"pkce\",\n        // Storage configuration for auth tokens\n        storage:  false ? 0 : undefined\n    },\n    // Global configuration\n    global: {\n        headers: {\n            \"X-Client-Info\": \"subdash-frontend\"\n        }\n    },\n    // Database configuration\n    db: {\n        schema: \"public\"\n    },\n    // Realtime configuration\n    realtime: {\n        params: {\n            eventsPerSecond: 10\n        }\n    }\n});\n// Helper functions for common database operations\nconst dbHelpers = {\n    // Businesses\n    async getBusinesses (userId) {\n        const { data, error } = await supabase.from(\"businesses\").select(\"*\").eq(\"user_id\", userId).order(\"created_at\", {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    async getBusiness (id, userId) {\n        const { data, error } = await supabase.from(\"businesses\").select(\"*\").eq(\"id\", id).eq(\"user_id\", userId).single();\n        if (error) throw error;\n        return data;\n    },\n    async createBusiness (business) {\n        const { data, error } = await supabase.from(\"businesses\").insert(business).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async updateBusiness (id, updates) {\n        const { data, error } = await supabase.from(\"businesses\").update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", id).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async deleteBusiness (id) {\n        const { error } = await supabase.from(\"businesses\").delete().eq(\"id\", id);\n        if (error) throw error;\n    },\n    // Subscriptions\n    async getSubscriptions (businessId) {\n        const { data, error } = await supabase.from(\"subscriptions\").select(\"*\").eq(\"business_id\", businessId).order(\"created_at\", {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    async getSubscription (id) {\n        const { data, error } = await supabase.from(\"subscriptions\").select(\"*\").eq(\"id\", id).single();\n        if (error) throw error;\n        return data;\n    },\n    async createSubscription (subscription) {\n        const { data, error } = await supabase.from(\"subscriptions\").insert(subscription).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async updateSubscription (id, updates) {\n        const { data, error } = await supabase.from(\"subscriptions\").update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", id).select().single();\n        if (error) throw error;\n        return data;\n    },\n    async deleteSubscription (id) {\n        const { error } = await supabase.from(\"subscriptions\").delete().eq(\"id\", id);\n        if (error) throw error;\n    },\n    // Payments\n    async getPayments (businessId) {\n        const { data, error } = await supabase.from(\"payments\").select(`\n        *,\n        subscriptions(name)\n      `).eq(\"business_id\", businessId).order(\"payment_date\", {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    async createPayment (payment) {\n        const { data, error } = await supabase.from(\"payments\").insert(payment).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Reimbursements\n    async getReimbursements (businessId) {\n        const { data, error } = await supabase.from(\"reimbursements\").select(\"*\").eq(\"business_id\", businessId).order(\"submitted_date\", {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    },\n    async createReimbursement (reimbursement) {\n        const { data, error } = await supabase.from(\"reimbursements\").insert(reimbursement).select().single();\n        if (error) throw error;\n        return data;\n    },\n    // Exchange rates\n    async getExchangeRate (fromCurrency, toCurrency, date) {\n        let query = supabase.from(\"exchange_rates\").select(\"*\").eq(\"from_currency\", fromCurrency).eq(\"to_currency\", toCurrency).order(\"date\", {\n            ascending: false\n        }).limit(1);\n        if (date) {\n            query = query.eq(\"date\", date);\n        }\n        const { data, error } = await query.single();\n        if (error) throw error;\n        return data;\n    }\n};\n// Real-time subscriptions helpers\nconst realtimeHelpers = {\n    subscribeToBusinesses (userId, callback) {\n        return supabase.channel(\"businesses\").on(\"postgres_changes\", {\n            event: \"*\",\n            schema: \"public\",\n            table: \"businesses\",\n            filter: `user_id=eq.${userId}`\n        }, callback).subscribe();\n    },\n    subscribeToSubscriptions (businessId, callback) {\n        return supabase.channel(\"subscriptions\").on(\"postgres_changes\", {\n            event: \"*\",\n            schema: \"public\",\n            table: \"subscriptions\",\n            filter: `business_id=eq.${businessId}`\n        }, callback).subscribe();\n    },\n    subscribeToPayments (businessId, callback) {\n        return supabase.channel(\"payments\").on(\"postgres_changes\", {\n            event: \"*\",\n            schema: \"public\",\n            table: \"payments\",\n            filter: `business_id=eq.${businessId}`\n        }, callback).subscribe();\n    },\n    subscribeToReimbursements (businessId, callback) {\n        return supabase.channel(\"reimbursements\").on(\"postgres_changes\", {\n            event: \"*\",\n            schema: \"public\",\n            table: \"reimbursements\",\n            filter: `business_id=eq.${businessId}`\n        }, callback).subscribe();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/supabase.ts\n");

/***/ }),

/***/ "./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculatePercentage: () => (/* binding */ calculatePercentage),\n/* harmony export */   camelToKebab: () => (/* binding */ camelToKebab),\n/* harmony export */   capitalizeWords: () => (/* binding */ capitalizeWords),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   downloadFile: () => (/* binding */ downloadFile),\n/* harmony export */   formatBytes: () => (/* binding */ formatBytes),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getContrastColor: () => (/* binding */ getContrastColor),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   getRandomColor: () => (/* binding */ getRandomColor),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isEmpty: () => (/* binding */ isEmpty),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   kebabToCamel: () => (/* binding */ kebabToCamel),\n/* harmony export */   objectToQueryString: () => (/* binding */ objectToQueryString),\n/* harmony export */   parseQueryString: () => (/* binding */ parseQueryString),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n/**\n * Utility function to merge Tailwind CSS classes\n * Combines clsx for conditional classes and tailwind-merge for deduplication\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format currency with proper locale and currency code\n */ /**\n * Format percentage with proper decimal places\n */ function formatPercentage(value, decimals = 1) {\n    return `${(value * 100).toFixed(decimals)}%`;\n}\nfunction formatCurrency(amount, currency = \"KWD\", locale = \"en-KW\") {\n    try {\n        return new Intl.NumberFormat(locale, {\n            style: \"currency\",\n            currency: currency,\n            minimumFractionDigits: currency === \"KWD\" ? 3 : 2,\n            maximumFractionDigits: currency === \"KWD\" ? 3 : 2\n        }).format(amount);\n    } catch (error) {\n        // Fallback for unsupported currencies\n        return `${currency} ${amount.toFixed(currency === \"KWD\" ? 3 : 2)}`;\n    }\n}\n/**\n * Format date with proper locale\n */ function formatDate(date, options = {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\"\n}, locale = \"en-US\") {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    return new Intl.DateTimeFormat(locale, options).format(dateObj);\n}\n/**\n * Format relative time (e.g., \"2 hours ago\")\n */ function formatRelativeTime(date, locale = \"en-US\") {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n    const rtf = new Intl.RelativeTimeFormat(locale, {\n        numeric: \"auto\"\n    });\n    if (diffInSeconds < 60) {\n        return rtf.format(-diffInSeconds, \"second\");\n    } else if (diffInSeconds < 3600) {\n        return rtf.format(-Math.floor(diffInSeconds / 60), \"minute\");\n    } else if (diffInSeconds < 86400) {\n        return rtf.format(-Math.floor(diffInSeconds / 3600), \"hour\");\n    } else if (diffInSeconds < 2592000) {\n        return rtf.format(-Math.floor(diffInSeconds / 86400), \"day\");\n    } else if (diffInSeconds < 31536000) {\n        return rtf.format(-Math.floor(diffInSeconds / 2592000), \"month\");\n    } else {\n        return rtf.format(-Math.floor(diffInSeconds / 31536000), \"year\");\n    }\n}\n/**\n * Truncate text with ellipsis\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + \"...\";\n}\n/**\n * Generate initials from a name\n */ function getInitials(name) {\n    return name.split(\" \").map((word)=>word.charAt(0).toUpperCase()).slice(0, 2).join(\"\");\n}\n/**\n * Validate email format\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Generate a random ID\n */ function generateId(length = 8) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * Debounce function\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Deep clone an object\n */ function deepClone(obj) {\n    if (obj === null || typeof obj !== \"object\") return obj;\n    if (obj instanceof Date) return new Date(obj.getTime());\n    if (obj instanceof Array) return obj.map((item)=>deepClone(item));\n    if (typeof obj === \"object\") {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n/**\n * Check if a value is empty (null, undefined, empty string, empty array, empty object)\n */ function isEmpty(value) {\n    if (value === null || value === undefined) return true;\n    if (typeof value === \"string\") return value.trim() === \"\";\n    if (Array.isArray(value)) return value.length === 0;\n    if (typeof value === \"object\") return Object.keys(value).length === 0;\n    return false;\n}\n/**\n * Convert bytes to human readable format\n */ function formatBytes(bytes, decimals = 2) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const dm = decimals < 0 ? 0 : decimals;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\",\n        \"TB\",\n        \"PB\",\n        \"EB\",\n        \"ZB\",\n        \"YB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + \" \" + sizes[i];\n}\n/**\n * Sleep function for async operations\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * Get contrast color (black or white) for a given background color\n */ function getContrastColor(hexColor) {\n    // Remove # if present\n    const color = hexColor.replace(\"#\", \"\");\n    // Convert to RGB\n    const r = parseInt(color.substr(0, 2), 16);\n    const g = parseInt(color.substr(2, 2), 16);\n    const b = parseInt(color.substr(4, 2), 16);\n    // Calculate luminance\n    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;\n    return luminance > 0.5 ? \"#000000\" : \"#ffffff\";\n}\n/**\n * Capitalize first letter of each word\n */ function capitalizeWords(str) {\n    return str.replace(/\\b\\w/g, (char)=>char.toUpperCase());\n}\n/**\n * Convert camelCase to kebab-case\n */ function camelToKebab(str) {\n    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, \"$1-$2\").toLowerCase();\n}\n/**\n * Convert kebab-case to camelCase\n */ function kebabToCamel(str) {\n    return str.replace(/-([a-z])/g, (_, letter)=>letter.toUpperCase());\n}\n/**\n * Parse query string to object\n */ function parseQueryString(queryString) {\n    const params = new URLSearchParams(queryString);\n    const result = {};\n    for (const [key, value] of params.entries()){\n        result[key] = value;\n    }\n    return result;\n}\n/**\n * Convert object to query string\n */ function objectToQueryString(obj) {\n    const params = new URLSearchParams();\n    for (const [key, value] of Object.entries(obj)){\n        if (value !== null && value !== undefined && value !== \"\") {\n            params.append(key, String(value));\n        }\n    }\n    return params.toString();\n}\n/**\n * Check if code is running in browser\n */ function isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\n/**\n * Get device type based on screen width\n */ function getDeviceType() {\n    if (!isBrowser()) return \"desktop\";\n    const width = window.innerWidth;\n    if (width < 768) return \"mobile\";\n    if (width < 1024) return \"tablet\";\n    return \"desktop\";\n}\n/**\n * Copy text to clipboard\n */ async function copyToClipboard(text) {\n    if (!isBrowser()) return false;\n    try {\n        if (navigator.clipboard && window.isSecureContext) {\n            await navigator.clipboard.writeText(text);\n            return true;\n        } else {\n            // Fallback for older browsers\n            const textArea = document.createElement(\"textarea\");\n            textArea.value = text;\n            textArea.style.position = \"fixed\";\n            textArea.style.left = \"-999999px\";\n            textArea.style.top = \"-999999px\";\n            document.body.appendChild(textArea);\n            textArea.focus();\n            textArea.select();\n            const result = document.execCommand(\"copy\");\n            textArea.remove();\n            return result;\n        }\n    } catch (error) {\n        console.error(\"Failed to copy text to clipboard:\", error);\n        return false;\n    }\n}\n/**\n * Download data as file\n */ function downloadFile(data, filename, type = \"text/plain\") {\n    if (!isBrowser()) return;\n    const blob = new Blob([\n        data\n    ], {\n        type\n    });\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement(\"a\");\n    link.href = url;\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    window.URL.revokeObjectURL(url);\n}\n/**\n * Format number with thousand separators\n */ function formatNumber(num, locale = \"en-US\") {\n    return new Intl.NumberFormat(locale).format(num);\n}\n/**\n * Calculate percentage\n */ function calculatePercentage(value, total) {\n    if (total === 0) return 0;\n    return Math.round(value / total * 100);\n}\n/**\n * Get random color from predefined palette\n */ function getRandomColor() {\n    const colors = [\n        \"#3B82F6\",\n        \"#10B981\",\n        \"#F59E0B\",\n        \"#EF4444\",\n        \"#8B5CF6\",\n        \"#06B6D4\",\n        \"#F97316\",\n        \"#84CC16\",\n        \"#EC4899\",\n        \"#6B7280\"\n    ];\n    return colors[Math.floor(Math.random() * colors.length)];\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/utils.ts\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"@tanstack/react-query\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"@tanstack/react-query-devtools\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/useAuth */ \"./hooks/useAuth.tsx\");\n/* harmony import */ var _hooks_useTheme__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/useTheme */ \"./hooks/useTheme.tsx\");\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/layout/Layout */ \"./components/layout/Layout.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_8__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__, _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__, _components_layout_Layout__WEBPACK_IMPORTED_MODULE_7__]);\n([_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__, _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__, _components_layout_Layout__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n// Create a client\nfunction createQueryClient() {\n    return new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClient({\n        defaultOptions: {\n            queries: {\n                // With SSR, we usually want to set some default staleTime\n                // above 0 to avoid refetching immediately on the client\n                staleTime: 60 * 1000,\n                retry: (failureCount, error)=>{\n                    // Don't retry on 4xx errors except 408, 429\n                    if (error?.status >= 400 && error?.status < 500 && ![\n                        408,\n                        429\n                    ].includes(error?.status)) {\n                        return false;\n                    }\n                    // Retry up to 3 times for other errors\n                    return failureCount < 3;\n                },\n                refetchOnWindowFocus: false\n            },\n            mutations: {\n                retry: false\n            }\n        }\n    });\n}\n// Pages that don't need the main layout\nconst NO_LAYOUT_PAGES = [\n    \"/auth/signin\",\n    \"/auth/signup\",\n    \"/auth/forgot-password\",\n    \"/auth/reset-password\",\n    \"/404\",\n    \"/500\"\n];\nfunction App({ Component, pageProps, router }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(()=>createQueryClient());\n    // Check if current page should have layout\n    const shouldHaveLayout = !NO_LAYOUT_PAGES.includes(router.pathname);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useTheme__WEBPACK_IMPORTED_MODULE_6__.ThemeProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__.AuthProvider, {\n                children: [\n                    shouldHaveLayout ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            ...pageProps\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_app.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_app.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                        ...pageProps\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_app.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"var(--color-background-primary)\",\n                                color: \"var(--color-text-primary)\",\n                                border: \"1px solid var(--color-border-primary)\",\n                                borderRadius: \"8px\",\n                                fontSize: \"14px\",\n                                maxWidth: \"400px\"\n                            },\n                            success: {\n                                iconTheme: {\n                                    primary: \"var(--color-success-500)\",\n                                    secondary: \"var(--color-success-50)\"\n                                }\n                            },\n                            error: {\n                                iconTheme: {\n                                    primary: \"var(--color-error-500)\",\n                                    secondary: \"var(--color-error-50)\"\n                                }\n                            },\n                            loading: {\n                                iconTheme: {\n                                    primary: \"var(--color-brand-500)\",\n                                    secondary: \"var(--color-brand-50)\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_app.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_2__.ReactQueryDevtools, {\n                        initialIsOpen: false\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_app.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_app.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_app.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_app.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"index,follow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"googlebot\",\n                        content: \"index,follow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"mask-icon\",\n                        href: \"/safari-pinned-tab.svg\",\n                        color: \"#1e40af\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#1e40af\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#ffffff\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"application-name\",\n                        content: \"SubDash\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"SubDash\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-config\",\n                        content: \"/browserconfig.xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-tap-highlight\",\n                        content: \"no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:site_name\",\n                        content: \"SubDash\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:locale\",\n                        content: \"en_US\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:creator\",\n                        content: \"@subdash\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-Content-Type-Options\",\n                        content: \"nosniff\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-Frame-Options\",\n                        content: \"DENY\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-XSS-Protection\",\n                        content: \"1; mode=block\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preload\",\n                        href: \"/fonts/inter-var.woff2\",\n                        as: \"font\",\n                        type: \"font/woff2\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.gstatic.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              // Prevent flash of unstyled content (FOUC)\n              (function() {\n                try {\n                  var theme = localStorage.getItem('theme');\n                  var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                  var activeTheme = theme === 'system' ? systemTheme : theme || 'light';\n                  \n                  if (activeTheme === 'dark') {\n                    document.documentElement.classList.add('dark');\n                  } else {\n                    document.documentElement.classList.remove('dark');\n                  }\n                  \n                  // Set CSS custom properties for theme\n                  document.documentElement.style.setProperty('--initial-theme', activeTheme);\n                } catch (e) {\n                  console.warn('Failed to set initial theme:', e);\n                }\n              })();\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"WebApplication\",\n                                name: \"SubDash\",\n                                description: \"Comprehensive subscription and business management dashboard\",\n                                url: \"http://localhost:3000\" || 0,\n                                applicationCategory: \"BusinessApplication\",\n                                operatingSystem: \"Web\",\n                                offers: {\n                                    \"@type\": \"Offer\",\n                                    price: \"0\",\n                                    priceCurrency: \"USD\"\n                                },\n                                author: {\n                                    \"@type\": \"Organization\",\n                                    name: \"SubDash Team\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"bg-background-primary text-text-primary antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"loading-screen\",\n                        className: \"fixed inset-0 z-50 flex items-center justify-center bg-background-primary\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 animate-spin rounded-full border-2 border-brand-500 border-t-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-text-secondary\",\n                                    children: \"Loading SubDash...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              window.addEventListener('DOMContentLoaded', function() {\n                setTimeout(function() {\n                  var loadingScreen = document.getElementById('loading-screen');\n                  if (loadingScreen) {\n                    loadingScreen.style.display = 'none';\n                  }\n                }, 100);\n              });\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                     false && /*#__PURE__*/ 0,\n                     false && /*#__PURE__*/ 0\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\subscription-dashboard\\\\frontend\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fZG9jdW1lbnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE2RDtBQUU5QyxTQUFTSTtJQUN0QixxQkFDRSw4REFBQ0osK0NBQUlBO1FBQUNLLE1BQUs7OzBCQUNULDhEQUFDSiwrQ0FBSUE7O2tDQUVILDhEQUFDSzt3QkFBS0MsU0FBUTs7Ozs7O2tDQUNkLDhEQUFDRDt3QkFBS0UsTUFBSzt3QkFBU0MsU0FBUTs7Ozs7O2tDQUM1Qiw4REFBQ0g7d0JBQUtFLE1BQUs7d0JBQVlDLFNBQVE7Ozs7OztrQ0FHL0IsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7a0NBQ3RCLDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBbUJFLE9BQU07d0JBQVVELE1BQUs7Ozs7OztrQ0FDbEQsOERBQUNGO3dCQUFLQyxLQUFJO3dCQUFPRyxNQUFLO3dCQUFZRCxPQUFNO3dCQUFRRCxNQUFLOzs7Ozs7a0NBQ3JELDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBT0csTUFBSzt3QkFBWUQsT0FBTTt3QkFBUUQsTUFBSzs7Ozs7O2tDQUNyRCw4REFBQ0Y7d0JBQUtDLEtBQUk7d0JBQVdDLE1BQUs7Ozs7OztrQ0FDMUIsOERBQUNGO3dCQUFLQyxLQUFJO3dCQUFZQyxNQUFLO3dCQUF5QkcsT0FBTTs7Ozs7O2tDQUMxRCw4REFBQ1Q7d0JBQUtFLE1BQUs7d0JBQTBCQyxTQUFROzs7Ozs7a0NBQzdDLDhEQUFDSDt3QkFBS0UsTUFBSzt3QkFBY0MsU0FBUTs7Ozs7O2tDQUdqQyw4REFBQ0M7d0JBQUtDLEtBQUk7d0JBQWFDLE1BQUs7Ozs7OztrQ0FDNUIsOERBQUNGO3dCQUFLQyxLQUFJO3dCQUFhQyxNQUFLO3dCQUE0QkksYUFBWTs7Ozs7O2tDQUdwRSw4REFBQ047d0JBQ0NFLE1BQUs7d0JBQ0xELEtBQUk7Ozs7OztrQ0FJTiw4REFBQ0w7d0JBQUtFLE1BQUs7d0JBQW1CQyxTQUFROzs7Ozs7a0NBQ3RDLDhEQUFDSDt3QkFBS0UsTUFBSzt3QkFBK0JDLFNBQVE7Ozs7OztrQ0FDbEQsOERBQUNIO3dCQUFLRSxNQUFLO3dCQUF3Q0MsU0FBUTs7Ozs7O2tDQUMzRCw4REFBQ0g7d0JBQUtFLE1BQUs7d0JBQTZCQyxTQUFROzs7Ozs7a0NBQ2hELDhEQUFDSDt3QkFBS0UsTUFBSzt3QkFBbUJDLFNBQVE7Ozs7OztrQ0FDdEMsOERBQUNIO3dCQUFLRSxNQUFLO3dCQUF5QkMsU0FBUTs7Ozs7O2tDQUM1Qyw4REFBQ0g7d0JBQUtFLE1BQUs7d0JBQXVCQyxTQUFROzs7Ozs7a0NBQzFDLDhEQUFDSDt3QkFBS0UsTUFBSzt3QkFBOEJDLFNBQVE7Ozs7OztrQ0FHakQsOERBQUNIO3dCQUFLVyxVQUFTO3dCQUFVUixTQUFROzs7Ozs7a0NBQ2pDLDhEQUFDSDt3QkFBS1csVUFBUzt3QkFBZVIsU0FBUTs7Ozs7O2tDQUN0Qyw4REFBQ0g7d0JBQUtXLFVBQVM7d0JBQVlSLFNBQVE7Ozs7OztrQ0FHbkMsOERBQUNIO3dCQUFLRSxNQUFLO3dCQUFlQyxTQUFROzs7Ozs7a0NBQ2xDLDhEQUFDSDt3QkFBS0UsTUFBSzt3QkFBa0JDLFNBQVE7Ozs7OztrQ0FHckMsOERBQUNIO3dCQUFLWSxXQUFVO3dCQUF5QlQsU0FBUTs7Ozs7O2tDQUNqRCw4REFBQ0g7d0JBQUtZLFdBQVU7d0JBQWtCVCxTQUFROzs7Ozs7a0NBQzFDLDhEQUFDSDt3QkFBS1ksV0FBVTt3QkFBbUJULFNBQVE7Ozs7OztrQ0FHM0MsOERBQUNIO3dCQUFLRSxNQUFLO3dCQUFtQkMsU0FBUTs7Ozs7O2tDQUd0Qyw4REFBQ0g7d0JBQ0NFLE1BQUs7d0JBQ0xDLFNBQVE7Ozs7OztrQ0FJViw4REFBQ0M7d0JBQUtDLEtBQUk7d0JBQVVDLE1BQUs7d0JBQXlCTyxJQUFHO3dCQUFPTCxNQUFLO3dCQUFhRSxhQUFZOzs7Ozs7a0NBRzFGLDhEQUFDTjt3QkFBS0MsS0FBSTt3QkFBZUMsTUFBSzs7Ozs7O2tDQUM5Qiw4REFBQ0Y7d0JBQUtDLEtBQUk7d0JBQWVDLE1BQUs7Ozs7OztrQ0FHOUIsOERBQUNRO3dCQUNDQyx5QkFBeUI7NEJBQ3ZCQyxRQUFRLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBb0JULENBQUM7d0JBQ0g7Ozs7OztrQ0FJRiw4REFBQ0Y7d0JBQ0NOLE1BQUs7d0JBQ0xPLHlCQUF5Qjs0QkFDdkJDLFFBQVFDLEtBQUtDLFNBQVMsQ0FBQztnQ0FDckIsWUFBWTtnQ0FDWixTQUFTO2dDQUNUaEIsTUFBTTtnQ0FDTmlCLGFBQWE7Z0NBQ2JDLEtBQUtDLHVCQUErQixJQUFJO2dDQUN4Q0cscUJBQXFCO2dDQUNyQkMsaUJBQWlCO2dDQUNqQkMsUUFBUTtvQ0FDTixTQUFTO29DQUNUQyxPQUFPO29DQUNQQyxlQUFlO2dDQUNqQjtnQ0FDQUMsUUFBUTtvQ0FDTixTQUFTO29DQUNUM0IsTUFBTTtnQ0FDUjs0QkFDRjt3QkFDRjs7Ozs7Ozs7Ozs7OzBCQUdKLDhEQUFDNEI7Z0JBQUtDLFdBQVU7O2tDQUVkLDhEQUFDQzt3QkFBSUMsSUFBRzt3QkFBaUJGLFdBQVU7a0NBQ2pDLDRFQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFJRCxXQUFVOzs7Ozs7OENBQ2YsOERBQUNHO29DQUFFSCxXQUFVOzhDQUE4Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSy9DLDhEQUFDakI7d0JBQ0NDLHlCQUF5Qjs0QkFDdkJDLFFBQVEsQ0FBQzs7Ozs7Ozs7O1lBU1QsQ0FBQzt3QkFDSDs7Ozs7O2tDQUdGLDhEQUFDcEIsK0NBQUlBOzs7OztrQ0FDTCw4REFBQ0MscURBQVVBOzs7OztvQkFySm5CLE1Bd0prQyxrQkFDeEI7b0JBekpWLE1Bc01rQyxrQkFDeEI7Ozs7Ozs7Ozs7Ozs7QUFxQlYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdWJzY3JpcHRpb24tZGFzaGJvYXJkLWZyb250ZW5kLy4vcGFnZXMvX2RvY3VtZW50LnRzeD9kMzdkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEh0bWwsIEhlYWQsIE1haW4sIE5leHRTY3JpcHQgfSBmcm9tICduZXh0L2RvY3VtZW50JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnQoKSB7XG4gIHJldHVybiAoXG4gICAgPEh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8SGVhZD5cbiAgICAgICAgey8qIE1ldGEgdGFncyAqL31cbiAgICAgICAgPG1ldGEgY2hhclNldD1cInV0Zi04XCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cInJvYm90c1wiIGNvbnRlbnQ9XCJpbmRleCxmb2xsb3dcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwiZ29vZ2xlYm90XCIgY29udGVudD1cImluZGV4LGZvbGxvd1wiIC8+XG4gICAgICAgIFxuICAgICAgICB7LyogRmF2aWNvbiBhbmQgYXBwIGljb25zICovfVxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImFwcGxlLXRvdWNoLWljb25cIiBzaXplcz1cIjE4MHgxODBcIiBocmVmPVwiL2FwcGxlLXRvdWNoLWljb24ucG5nXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIHR5cGU9XCJpbWFnZS9wbmdcIiBzaXplcz1cIjMyeDMyXCIgaHJlZj1cIi9mYXZpY29uLTMyeDMyLnBuZ1wiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImljb25cIiB0eXBlPVwiaW1hZ2UvcG5nXCIgc2l6ZXM9XCIxNngxNlwiIGhyZWY9XCIvZmF2aWNvbi0xNngxNi5wbmdcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJtYW5pZmVzdFwiIGhyZWY9XCIvc2l0ZS53ZWJtYW5pZmVzdFwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cIm1hc2staWNvblwiIGhyZWY9XCIvc2FmYXJpLXBpbm5lZC10YWIuc3ZnXCIgY29sb3I9XCIjMWU0MGFmXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cIm1zYXBwbGljYXRpb24tVGlsZUNvbG9yXCIgY29udGVudD1cIiMxZTQwYWZcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwidGhlbWUtY29sb3JcIiBjb250ZW50PVwiI2ZmZmZmZlwiIC8+XG4gICAgICAgIFxuICAgICAgICB7LyogUHJlY29ubmVjdCB0byBleHRlcm5hbCBkb21haW5zICovfVxuICAgICAgICA8bGluayByZWw9XCJwcmVjb25uZWN0XCIgaHJlZj1cImh0dHBzOi8vZm9udHMuZ29vZ2xlYXBpcy5jb21cIiAvPlxuICAgICAgICA8bGluayByZWw9XCJwcmVjb25uZWN0XCIgaHJlZj1cImh0dHBzOi8vZm9udHMuZ3N0YXRpYy5jb21cIiBjcm9zc09yaWdpbj1cImFub255bW91c1wiIC8+XG4gICAgICAgIFxuICAgICAgICB7LyogR29vZ2xlIEZvbnRzICovfVxuICAgICAgICA8bGlua1xuICAgICAgICAgIGhyZWY9XCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2NzczI/ZmFtaWx5PUludGVyOndnaHRAMzAwOzQwMDs1MDA7NjAwOzcwMCZmYW1pbHk9SmV0QnJhaW5zK01vbm86d2dodEA0MDA7NTAwOzYwMCZkaXNwbGF5PXN3YXBcIlxuICAgICAgICAgIHJlbD1cInN0eWxlc2hlZXRcIlxuICAgICAgICAvPlxuICAgICAgICBcbiAgICAgICAgey8qIFBXQSBtZXRhIHRhZ3MgKi99XG4gICAgICAgIDxtZXRhIG5hbWU9XCJhcHBsaWNhdGlvbi1uYW1lXCIgY29udGVudD1cIlN1YkRhc2hcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwiYXBwbGUtbW9iaWxlLXdlYi1hcHAtY2FwYWJsZVwiIGNvbnRlbnQ9XCJ5ZXNcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwiYXBwbGUtbW9iaWxlLXdlYi1hcHAtc3RhdHVzLWJhci1zdHlsZVwiIGNvbnRlbnQ9XCJkZWZhdWx0XCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImFwcGxlLW1vYmlsZS13ZWItYXBwLXRpdGxlXCIgY29udGVudD1cIlN1YkRhc2hcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwiZm9ybWF0LWRldGVjdGlvblwiIGNvbnRlbnQ9XCJ0ZWxlcGhvbmU9bm9cIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwibW9iaWxlLXdlYi1hcHAtY2FwYWJsZVwiIGNvbnRlbnQ9XCJ5ZXNcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwibXNhcHBsaWNhdGlvbi1jb25maWdcIiBjb250ZW50PVwiL2Jyb3dzZXJjb25maWcueG1sXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cIm1zYXBwbGljYXRpb24tdGFwLWhpZ2hsaWdodFwiIGNvbnRlbnQ9XCJub1wiIC8+XG4gICAgICAgIFxuICAgICAgICB7LyogT3BlbiBHcmFwaCBtZXRhIHRhZ3MgKi99XG4gICAgICAgIDxtZXRhIHByb3BlcnR5PVwib2c6dHlwZVwiIGNvbnRlbnQ9XCJ3ZWJzaXRlXCIgLz5cbiAgICAgICAgPG1ldGEgcHJvcGVydHk9XCJvZzpzaXRlX25hbWVcIiBjb250ZW50PVwiU3ViRGFzaFwiIC8+XG4gICAgICAgIDxtZXRhIHByb3BlcnR5PVwib2c6bG9jYWxlXCIgY29udGVudD1cImVuX1VTXCIgLz5cbiAgICAgICAgXG4gICAgICAgIHsvKiBUd2l0dGVyIG1ldGEgdGFncyAqL31cbiAgICAgICAgPG1ldGEgbmFtZT1cInR3aXR0ZXI6Y2FyZFwiIGNvbnRlbnQ9XCJzdW1tYXJ5X2xhcmdlX2ltYWdlXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cInR3aXR0ZXI6Y3JlYXRvclwiIGNvbnRlbnQ9XCJAc3ViZGFzaFwiIC8+XG4gICAgICAgIFxuICAgICAgICB7LyogU2VjdXJpdHkgaGVhZGVycyAqL31cbiAgICAgICAgPG1ldGEgaHR0cEVxdWl2PVwiWC1Db250ZW50LVR5cGUtT3B0aW9uc1wiIGNvbnRlbnQ9XCJub3NuaWZmXCIgLz5cbiAgICAgICAgPG1ldGEgaHR0cEVxdWl2PVwiWC1GcmFtZS1PcHRpb25zXCIgY29udGVudD1cIkRFTllcIiAvPlxuICAgICAgICA8bWV0YSBodHRwRXF1aXY9XCJYLVhTUy1Qcm90ZWN0aW9uXCIgY29udGVudD1cIjE7IG1vZGU9YmxvY2tcIiAvPlxuICAgICAgICBcbiAgICAgICAgey8qIERpc2FibGUgYXV0b21hdGljIHBob25lIG51bWJlciBkZXRlY3Rpb24gKi99XG4gICAgICAgIDxtZXRhIG5hbWU9XCJmb3JtYXQtZGV0ZWN0aW9uXCIgY29udGVudD1cInRlbGVwaG9uZT1ub1wiIC8+XG4gICAgICAgIFxuICAgICAgICB7LyogUHJldmVudCB6b29tIG9uIGlPUyAqL31cbiAgICAgICAgPG1ldGFcbiAgICAgICAgICBuYW1lPVwidmlld3BvcnRcIlxuICAgICAgICAgIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MSwgbWF4aW11bS1zY2FsZT0xLCB1c2VyLXNjYWxhYmxlPW5vXCJcbiAgICAgICAgLz5cbiAgICAgICAgXG4gICAgICAgIHsvKiBQcmVsb2FkIGNyaXRpY2FsIHJlc291cmNlcyAqL31cbiAgICAgICAgPGxpbmsgcmVsPVwicHJlbG9hZFwiIGhyZWY9XCIvZm9udHMvaW50ZXItdmFyLndvZmYyXCIgYXM9XCJmb250XCIgdHlwZT1cImZvbnQvd29mZjJcIiBjcm9zc09yaWdpbj1cImFub255bW91c1wiIC8+XG4gICAgICAgIFxuICAgICAgICB7LyogRE5TIHByZWZldGNoIGZvciBleHRlcm5hbCByZXNvdXJjZXMgKi99XG4gICAgICAgIDxsaW5rIHJlbD1cImRucy1wcmVmZXRjaFwiIGhyZWY9XCIvL2ZvbnRzLmdvb2dsZWFwaXMuY29tXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiZG5zLXByZWZldGNoXCIgaHJlZj1cIi8vZm9udHMuZ3N0YXRpYy5jb21cIiAvPlxuICAgICAgICBcbiAgICAgICAgey8qIENyaXRpY2FsIENTUyBmb3IgdGhlbWUgKi99XG4gICAgICAgIDxzY3JpcHRcbiAgICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTD17e1xuICAgICAgICAgICAgX19odG1sOiBgXG4gICAgICAgICAgICAgIC8vIFByZXZlbnQgZmxhc2ggb2YgdW5zdHlsZWQgY29udGVudCAoRk9VQylcbiAgICAgICAgICAgICAgKGZ1bmN0aW9uKCkge1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICB2YXIgdGhlbWUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndGhlbWUnKTtcbiAgICAgICAgICAgICAgICAgIHZhciBzeXN0ZW1UaGVtZSA9IHdpbmRvdy5tYXRjaE1lZGlhKCcocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspJykubWF0Y2hlcyA/ICdkYXJrJyA6ICdsaWdodCc7XG4gICAgICAgICAgICAgICAgICB2YXIgYWN0aXZlVGhlbWUgPSB0aGVtZSA9PT0gJ3N5c3RlbScgPyBzeXN0ZW1UaGVtZSA6IHRoZW1lIHx8ICdsaWdodCc7XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIGlmIChhY3RpdmVUaGVtZSA9PT0gJ2RhcmsnKSB7XG4gICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGFzc0xpc3QuYWRkKCdkYXJrJyk7XG4gICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LnJlbW92ZSgnZGFyaycpO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAvLyBTZXQgQ1NTIGN1c3RvbSBwcm9wZXJ0aWVzIGZvciB0aGVtZVxuICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLnNldFByb3BlcnR5KCctLWluaXRpYWwtdGhlbWUnLCBhY3RpdmVUaGVtZSk7XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCdGYWlsZWQgdG8gc2V0IGluaXRpYWwgdGhlbWU6JywgZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9KSgpO1xuICAgICAgICAgICAgYCxcbiAgICAgICAgICB9fVxuICAgICAgICAvPlxuICAgICAgICBcbiAgICAgICAgey8qIFN0cnVjdHVyZWQgZGF0YSBmb3IgU0VPICovfVxuICAgICAgICA8c2NyaXB0XG4gICAgICAgICAgdHlwZT1cImFwcGxpY2F0aW9uL2xkK2pzb25cIlxuICAgICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MPXt7XG4gICAgICAgICAgICBfX2h0bWw6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICAgICAgJ0Bjb250ZXh0JzogJ2h0dHBzOi8vc2NoZW1hLm9yZycsXG4gICAgICAgICAgICAgICdAdHlwZSc6ICdXZWJBcHBsaWNhdGlvbicsXG4gICAgICAgICAgICAgIG5hbWU6ICdTdWJEYXNoJyxcbiAgICAgICAgICAgICAgZGVzY3JpcHRpb246ICdDb21wcmVoZW5zaXZlIHN1YnNjcmlwdGlvbiBhbmQgYnVzaW5lc3MgbWFuYWdlbWVudCBkYXNoYm9hcmQnLFxuICAgICAgICAgICAgICB1cmw6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQUF9VUkwgfHwgJ2h0dHBzOi8vc3ViZGFzaC5hcHAnLFxuICAgICAgICAgICAgICBhcHBsaWNhdGlvbkNhdGVnb3J5OiAnQnVzaW5lc3NBcHBsaWNhdGlvbicsXG4gICAgICAgICAgICAgIG9wZXJhdGluZ1N5c3RlbTogJ1dlYicsXG4gICAgICAgICAgICAgIG9mZmVyczoge1xuICAgICAgICAgICAgICAgICdAdHlwZSc6ICdPZmZlcicsXG4gICAgICAgICAgICAgICAgcHJpY2U6ICcwJyxcbiAgICAgICAgICAgICAgICBwcmljZUN1cnJlbmN5OiAnVVNEJyxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgYXV0aG9yOiB7XG4gICAgICAgICAgICAgICAgJ0B0eXBlJzogJ09yZ2FuaXphdGlvbicsXG4gICAgICAgICAgICAgICAgbmFtZTogJ1N1YkRhc2ggVGVhbScsXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9KSxcbiAgICAgICAgICB9fVxuICAgICAgICAvPlxuICAgICAgPC9IZWFkPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiYmctYmFja2dyb3VuZC1wcmltYXJ5IHRleHQtdGV4dC1wcmltYXJ5IGFudGlhbGlhc2VkXCI+XG4gICAgICAgIHsvKiBMb2FkaW5nIHNjcmVlbiAqL31cbiAgICAgICAgPGRpdiBpZD1cImxvYWRpbmctc2NyZWVuXCIgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCB6LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLWJhY2tncm91bmQtcHJpbWFyeVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgc3BhY2UteS00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCB3LTggYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBib3JkZXItMiBib3JkZXItYnJhbmQtNTAwIGJvcmRlci10LXRyYW5zcGFyZW50XCI+PC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtdGV4dC1zZWNvbmRhcnlcIj5Mb2FkaW5nIFN1YkRhc2guLi48L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgey8qIEhpZGUgbG9hZGluZyBzY3JlZW4gd2hlbiBSZWFjdCBoeWRyYXRlcyAqL31cbiAgICAgICAgPHNjcmlwdFxuICAgICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MPXt7XG4gICAgICAgICAgICBfX2h0bWw6IGBcbiAgICAgICAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ0RPTUNvbnRlbnRMb2FkZWQnLCBmdW5jdGlvbigpIHtcbiAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uKCkge1xuICAgICAgICAgICAgICAgICAgdmFyIGxvYWRpbmdTY3JlZW4gPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnbG9hZGluZy1zY3JlZW4nKTtcbiAgICAgICAgICAgICAgICAgIGlmIChsb2FkaW5nU2NyZWVuKSB7XG4gICAgICAgICAgICAgICAgICAgIGxvYWRpbmdTY3JlZW4uc3R5bGUuZGlzcGxheSA9ICdub25lJztcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9LCAxMDApO1xuICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGAsXG4gICAgICAgICAgfX1cbiAgICAgICAgLz5cbiAgICAgICAgXG4gICAgICAgIDxNYWluIC8+XG4gICAgICAgIDxOZXh0U2NyaXB0IC8+XG4gICAgICAgIFxuICAgICAgICB7LyogQW5hbHl0aWNzIGFuZCB0cmFja2luZyBzY3JpcHRzICovfVxuICAgICAgICB7cHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJyAmJiAoXG4gICAgICAgICAgPD5cbiAgICAgICAgICAgIHsvKiBHb29nbGUgQW5hbHl0aWNzICovfVxuICAgICAgICAgICAge3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0dBX0lEICYmIChcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICA8c2NyaXB0XG4gICAgICAgICAgICAgICAgICBhc3luY1xuICAgICAgICAgICAgICAgICAgc3JjPXtgaHR0cHM6Ly93d3cuZ29vZ2xldGFnbWFuYWdlci5jb20vZ3RhZy9qcz9pZD0ke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0dBX0lEfWB9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8c2NyaXB0XG4gICAgICAgICAgICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTD17e1xuICAgICAgICAgICAgICAgICAgICBfX2h0bWw6IGBcbiAgICAgICAgICAgICAgICAgICAgICB3aW5kb3cuZGF0YUxheWVyID0gd2luZG93LmRhdGFMYXllciB8fCBbXTtcbiAgICAgICAgICAgICAgICAgICAgICBmdW5jdGlvbiBndGFnKCl7ZGF0YUxheWVyLnB1c2goYXJndW1lbnRzKTt9XG4gICAgICAgICAgICAgICAgICAgICAgZ3RhZygnanMnLCBuZXcgRGF0ZSgpKTtcbiAgICAgICAgICAgICAgICAgICAgICBndGFnKCdjb25maWcnLCAnJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19HQV9JRH0nLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICBwYWdlX3RpdGxlOiBkb2N1bWVudC50aXRsZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhZ2VfbG9jYXRpb246IHdpbmRvdy5sb2NhdGlvbi5ocmVmLFxuICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICBgLFxuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBIb3RqYXIgKi99XG4gICAgICAgICAgICB7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfSE9USkFSX0lEICYmIChcbiAgICAgICAgICAgICAgPHNjcmlwdFxuICAgICAgICAgICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MPXt7XG4gICAgICAgICAgICAgICAgICBfX2h0bWw6IGBcbiAgICAgICAgICAgICAgICAgICAgKGZ1bmN0aW9uKGgsbyx0LGosYSxyKXtcbiAgICAgICAgICAgICAgICAgICAgICBoLmhqPWguaGp8fGZ1bmN0aW9uKCl7KGguaGoucT1oLmhqLnF8fFtdKS5wdXNoKGFyZ3VtZW50cyl9O1xuICAgICAgICAgICAgICAgICAgICAgIGguX2hqU2V0dGluZ3M9e2hqaWQ6JHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19IT1RKQVJfSUR9LGhqc3Y6Nn07XG4gICAgICAgICAgICAgICAgICAgICAgYT1vLmdldEVsZW1lbnRzQnlUYWdOYW1lKCdoZWFkJylbMF07XG4gICAgICAgICAgICAgICAgICAgICAgcj1vLmNyZWF0ZUVsZW1lbnQoJ3NjcmlwdCcpO3IuYXN5bmM9MTtcbiAgICAgICAgICAgICAgICAgICAgICByLnNyYz10K2guX2hqU2V0dGluZ3MuaGppZCtqK2guX2hqU2V0dGluZ3MuaGpzdjtcbiAgICAgICAgICAgICAgICAgICAgICBhLmFwcGVuZENoaWxkKHIpO1xuICAgICAgICAgICAgICAgICAgICB9KSh3aW5kb3csZG9jdW1lbnQsJ2h0dHBzOi8vc3RhdGljLmhvdGphci5jb20vYy9ob3RqYXItJywnLmpzP3N2PScpO1xuICAgICAgICAgICAgICAgICAgYCxcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8Lz5cbiAgICAgICAgKX1cbiAgICAgICAgXG4gICAgICAgIHsvKiBTZXJ2aWNlIFdvcmtlciByZWdpc3RyYXRpb24gKi99XG4gICAgICAgIHtwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nICYmIChcbiAgICAgICAgICA8c2NyaXB0XG4gICAgICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTD17e1xuICAgICAgICAgICAgICBfX2h0bWw6IGBcbiAgICAgICAgICAgICAgICBpZiAoJ3NlcnZpY2VXb3JrZXInIGluIG5hdmlnYXRvcikge1xuICAgICAgICAgICAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2xvYWQnLCBmdW5jdGlvbigpIHtcbiAgICAgICAgICAgICAgICAgICAgbmF2aWdhdG9yLnNlcnZpY2VXb3JrZXIucmVnaXN0ZXIoJy9zdy5qcycpXG4gICAgICAgICAgICAgICAgICAgICAgLnRoZW4oZnVuY3Rpb24ocmVnaXN0cmF0aW9uKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnU1cgcmVnaXN0ZXJlZDogJywgcmVnaXN0cmF0aW9uKTtcbiAgICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICAgIC5jYXRjaChmdW5jdGlvbihyZWdpc3RyYXRpb25FcnJvcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1NXIHJlZ2lzdHJhdGlvbiBmYWlsZWQ6ICcsIHJlZ2lzdHJhdGlvbkVycm9yKTtcbiAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgYCxcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgLz5cbiAgICAgICAgKX1cbiAgICAgIDwvYm9keT5cbiAgICA8L0h0bWw+XG4gICk7XG59Il0sIm5hbWVzIjpbIkh0bWwiLCJIZWFkIiwiTWFpbiIsIk5leHRTY3JpcHQiLCJEb2N1bWVudCIsImxhbmciLCJtZXRhIiwiY2hhclNldCIsIm5hbWUiLCJjb250ZW50IiwibGluayIsInJlbCIsImhyZWYiLCJzaXplcyIsInR5cGUiLCJjb2xvciIsImNyb3NzT3JpZ2luIiwicHJvcGVydHkiLCJodHRwRXF1aXYiLCJhcyIsInNjcmlwdCIsImRhbmdlcm91c2x5U2V0SW5uZXJIVE1MIiwiX19odG1sIiwiSlNPTiIsInN0cmluZ2lmeSIsImRlc2NyaXB0aW9uIiwidXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQUF9VUkwiLCJhcHBsaWNhdGlvbkNhdGVnb3J5Iiwib3BlcmF0aW5nU3lzdGVtIiwib2ZmZXJzIiwicHJpY2UiLCJwcmljZUN1cnJlbmN5IiwiYXV0aG9yIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiIsImlkIiwicCIsIk5FWFRfUFVCTElDX0dBX0lEIiwiYXN5bmMiLCJzcmMiLCJORVhUX1BVQkxJQ19IT1RKQVJfSUQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "@tanstack/react-query":
/*!****************************************!*\
  !*** external "@tanstack/react-query" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@tanstack/react-query");;

/***/ }),

/***/ "@tanstack/react-query-devtools":
/*!*************************************************!*\
  !*** external "@tanstack/react-query-devtools" ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@tanstack/react-query-devtools");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();