import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { useAuth } from '../../hooks/useAuth';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { api } from '../../lib/api';
import { formatDate, formatCurrency, cn } from '../../lib/utils';
import {
  PlusIcon,
  BuildingOfficeIcon,
  EllipsisVerticalIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';
import { BuildingOfficeIcon as BuildingOfficeSolidIcon } from '@heroicons/react/24/solid';

interface Business {
  id: string;
  name: string;
  description?: string;
  industry?: string;
  website?: string;
  phone?: string;
  email?: string;
  address?: string;
  city?: string;
  country?: string;
  currency: string;
  timezone: string;
  created_at: string;
  updated_at: string;
  _count?: {
    subscriptions: number;
    payments: number;
    reimbursements: number;
  };
  _sum?: {
    monthly_spend: number;
  };
}

export default function BusinessesPage() {
  const router = useRouter();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'created_at' | 'monthly_spend'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Fetch businesses
  const {
    data: businesses = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ['businesses'],
    queryFn: async () => {
      const response = await api.businesses.getAll();
      return response.data || [];
    },
    enabled: !!user,
  });

  // Delete business mutation
  const deleteMutation = useMutation({
    mutationFn: (businessId: string) => api.businesses.delete(businessId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['businesses'] });
      toast.success('Business deleted successfully');
      setShowDeleteModal(false);
      setSelectedBusiness(null);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete business');
    },
  });

  // Filter and sort businesses
  const filteredAndSortedBusinesses = businesses
    .filter((business: any) =>
      business.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      business.industry?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      business.description?.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a: any, b: any) => {
      let aValue: any;
      let bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'created_at':
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
        case 'monthly_spend':
          aValue = a._sum?.monthly_spend || 0;
          bValue = b._sum?.monthly_spend || 0;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleDeleteBusiness = () => {
    if (selectedBusiness) {
      deleteMutation.mutate(selectedBusiness.id);
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" text="Loading businesses..." />
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="text-center py-12">
          <div className="text-error-600 mb-4">Failed to load businesses</div>
          <button
            onClick={() => queryClient.invalidateQueries({ queryKey: ['businesses'] })}
            className="btn-primary"
          >
            Try Again
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Businesses - SubDash</title>
        <meta name="description" content="Manage your businesses" />
      </Head>

      <Layout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-text-primary">Businesses</h1>
              <p className="mt-1 text-sm text-text-secondary">
                Manage your business entities and their subscription data
              </p>
            </div>
            <div className="mt-4 sm:mt-0">
              <Link
                href="/businesses/new"
                className="btn-primary inline-flex items-center"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Business
              </Link>
            </div>
          </div>

          {/* Search and filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search businesses..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-primary w-full"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as typeof sortBy)}
                className="input-primary"
              >
                <option value="name">Sort by Name</option>
                <option value="created_at">Sort by Date</option>
                <option value="monthly_spend">Sort by Spend</option>
              </select>
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="btn-secondary px-3"
                title={`Sort ${sortOrder === 'asc' ? 'descending' : 'ascending'}`}
              >
                {sortOrder === 'asc' ? '↑' : '↓'}
              </button>
            </div>
          </div>

          {/* Businesses grid */}
          {filteredAndSortedBusinesses.length === 0 ? (
            <div className="text-center py-12">
              {searchTerm ? (
                <div>
                  <BuildingOfficeIcon className="mx-auto h-12 w-12 text-text-tertiary" />
                  <h3 className="mt-2 text-sm font-medium text-text-primary">No businesses found</h3>
                  <p className="mt-1 text-sm text-text-secondary">
                    No businesses match your search criteria.
                  </p>
                  <button
                    onClick={() => setSearchTerm('')}
                    className="mt-4 btn-secondary"
                  >
                    Clear search
                  </button>
                </div>
              ) : (
                <div>
                  <BuildingOfficeSolidIcon className="mx-auto h-12 w-12 text-text-tertiary" />
                  <h3 className="mt-2 text-sm font-medium text-text-primary">No businesses yet</h3>
                  <p className="mt-1 text-sm text-text-secondary">
                    Get started by creating your first business.
                  </p>
                  <Link href="/businesses/new" className="mt-4 btn-primary inline-flex items-center">
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add Business
                  </Link>
                </div>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredAndSortedBusinesses.map((business: any) => (
                <div
                  key={business.id}
                  className="card hover:shadow-lg transition-shadow duration-200"
                >
                  {/* Card header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 bg-brand-100 rounded-lg flex items-center justify-center">
                          <BuildingOfficeSolidIcon className="h-6 w-6 text-brand-600" />
                        </div>
                      </div>
                      <div className="min-w-0 flex-1">
                        <h3 className="text-lg font-medium text-text-primary truncate">
                          {business.name}
                        </h3>
                        {business.industry && (
                          <p className="text-sm text-text-secondary truncate">
                            {business.industry}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    {/* Actions dropdown */}
                    <div className="relative group">
                      <button className="p-1 rounded-md text-text-tertiary hover:text-text-primary hover:bg-background-secondary transition-colors">
                        <EllipsisVerticalIcon className="h-5 w-5" />
                      </button>
                      <div className="absolute right-0 top-8 w-48 bg-background-primary border border-border-primary rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                        <div className="py-1">
                          <Link
                            href={`/businesses/${business.id}`}
                            className="flex items-center px-4 py-2 text-sm text-text-primary hover:bg-background-secondary transition-colors"
                          >
                            <EyeIcon className="h-4 w-4 mr-3" />
                            View Details
                          </Link>
                          <Link
                            href={`/businesses/${business.id}/edit`}
                            className="flex items-center px-4 py-2 text-sm text-text-primary hover:bg-background-secondary transition-colors"
                          >
                            <PencilIcon className="h-4 w-4 mr-3" />
                            Edit
                          </Link>
                          <button
                            onClick={() => {
                              setSelectedBusiness(business);
                              setShowDeleteModal(true);
                            }}
                            className="flex items-center w-full px-4 py-2 text-sm text-error-600 hover:bg-error-50 transition-colors"
                          >
                            <TrashIcon className="h-4 w-4 mr-3" />
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Business description */}
                  {business.description && (
                    <p className="text-sm text-text-secondary mb-4 line-clamp-2">
                      {business.description}
                    </p>
                  )}

                  {/* Business stats */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <ChartBarIcon className="h-4 w-4 text-text-tertiary mr-1" />
                        <span className="text-xs text-text-tertiary">Subscriptions</span>
                      </div>
                      <div className="text-lg font-semibold text-text-primary">
                        {business._count?.subscriptions || 0}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <CurrencyDollarIcon className="h-4 w-4 text-text-tertiary mr-1" />
                        <span className="text-xs text-text-tertiary">Monthly Spend</span>
                      </div>
                      <div className="text-lg font-semibold text-text-primary">
                        {formatCurrency(business._sum?.monthly_spend || 0, business.currency)}
                      </div>
                    </div>
                  </div>

                  {/* Business info */}
                  <div className="space-y-2 text-xs text-text-secondary">
                    <div className="flex items-center">
                      <CalendarIcon className="h-3 w-3 mr-2" />
                      <span>Created {formatDate(business.created_at)}</span>
                    </div>
                    {business.website && (
                      <div className="flex items-center">
                        <span className="w-3 h-3 mr-2">🌐</span>
                        <a
                          href={business.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-brand-600 hover:text-brand-700 truncate"
                        >
                          {business.website.replace(/^https?:\/\//, '')}
                        </a>
                      </div>
                    )}
                    {business.city && business.country && (
                      <div className="flex items-center">
                        <span className="w-3 h-3 mr-2">📍</span>
                        <span className="truncate">{business.city}, {business.country}</span>
                      </div>
                    )}
                  </div>

                  {/* Quick actions */}
                  <div className="mt-4 pt-4 border-t border-border-primary">
                    <div className="flex space-x-2">
                      <Link
                        href={`/businesses/${business.id}`}
                        className="flex-1 btn-secondary text-center text-xs py-2"
                      >
                        View
                      </Link>
                      <Link
                        href={`/businesses/${business.id}/edit`}
                        className="flex-1 btn-primary text-center text-xs py-2"
                      >
                        Edit
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Delete confirmation modal */}
        {showDeleteModal && selectedBusiness && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-background-primary rounded-lg shadow-xl max-w-md w-full">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className="flex-shrink-0">
                    <TrashIcon className="h-6 w-6 text-error-600" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-lg font-medium text-text-primary">
                      Delete Business
                    </h3>
                  </div>
                </div>
                <div className="mb-6">
                  <p className="text-sm text-text-secondary">
                    Are you sure you want to delete <strong>{selectedBusiness.name}</strong>?
                    This action cannot be undone and will also delete all associated subscriptions,
                    payments, and reimbursements.
                  </p>
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={() => {
                      setShowDeleteModal(false);
                      setSelectedBusiness(null);
                    }}
                    className="flex-1 btn-secondary"
                    disabled={deleteMutation.isPending}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDeleteBusiness}
                    disabled={deleteMutation.isPending}
                    className="flex-1 bg-error-600 text-white px-4 py-2 rounded-md hover:bg-error-700 focus:outline-none focus:ring-2 focus:ring-error-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {deleteMutation.isPending ? (
                      <LoadingSpinner size="sm" className="text-white" />
                    ) : (
                      'Delete'
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </Layout>
    </>
  );
}