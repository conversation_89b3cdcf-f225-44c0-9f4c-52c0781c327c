import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import toast from 'react-hot-toast';
import Layout from '../../components/layout/Layout';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { api } from '../../lib/api';
import { formatCurrency } from '../../lib/utils';
import {
  CreditCardIcon,
  BuildingOfficeIcon,
  DocumentTextIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';

// Form validation schema
const paymentSchema = z.object({
  subscription_id: z.string().min(1, 'Please select a subscription'),
  business_id: z.string().min(1, 'Please select a business'),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  currency: z.string().min(1, 'Please select a currency'),
  payment_date: z.string().min(1, 'Payment date is required'),
  payment_method: z.enum(['credit_card', 'bank_transfer', 'paypal', 'other'], {
    required_error: 'Please select a payment method',
  }),
  status: z.enum(['completed', 'pending', 'failed'], {
    required_error: 'Please select a status',
  }),
  description: z.string().optional(),
  transaction_id: z.string().optional(),
  notes: z.string().optional(),
});

type PaymentFormData = z.infer<typeof paymentSchema>;

// Common currencies
const currencies = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
];

// Payment methods
const paymentMethods = [
  { value: 'credit_card', label: 'Credit Card', icon: CreditCardIcon },
  { value: 'bank_transfer', label: 'Bank Transfer', icon: BuildingOfficeIcon },
  { value: 'paypal', label: 'PayPal', icon: DocumentTextIcon },
  { value: 'other', label: 'Other', icon: DocumentTextIcon },
];

// Payment statuses
const paymentStatuses = [
  { value: 'completed', label: 'Completed', description: 'Payment has been successfully processed' },
  { value: 'pending', label: 'Pending', description: 'Payment is being processed' },
  { value: 'failed', label: 'Failed', description: 'Payment failed to process' },
];

export default function NewPaymentPage() {
  const router = useRouter();
  const [selectedBusiness, setSelectedBusiness] = useState<string>('');
  const [filteredSubscriptions, setFilteredSubscriptions] = useState<any[]>([]);

  // Fetch businesses
  const { data: businesses = [], isLoading: businessesLoading } = useQuery({
    queryKey: ['businesses'],
    queryFn: () => api.businesses.getAll(),
  });

  // Fetch subscriptions
  const { data: subscriptions = [], isLoading: subscriptionsLoading } = useQuery({
    queryKey: ['subscriptions'],
    queryFn: () => api.subscriptions.getAll(),
  });

  // Form setup
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      payment_date: new Date().toISOString().split('T')[0],
      status: 'completed',
      currency: 'USD',
      payment_method: 'credit_card',
    },
  });

  const watchedBusinessId = watch('business_id');
  const watchedSubscriptionId = watch('subscription_id');
  const watchedAmount = watch('amount');
  const watchedCurrency = watch('currency');

  // Filter subscriptions based on selected business
  useEffect(() => {
    if (watchedBusinessId) {
      const filtered = subscriptions.filter(
        (sub: any) => sub.business_id === watchedBusinessId
      );
      setFilteredSubscriptions(filtered);
      
      // Reset subscription selection if current selection is not valid for the new business
      if (watchedSubscriptionId) {
        const isValidSubscription = filtered.some((sub: any) => sub.id === watchedSubscriptionId);
        if (!isValidSubscription) {
          setValue('subscription_id', '');
        }
      }
    } else {
      setFilteredSubscriptions([]);
      setValue('subscription_id', '');
    }
  }, [watchedBusinessId, subscriptions, watchedSubscriptionId, setValue]);

  // Auto-fill amount and currency from selected subscription
  useEffect(() => {
    if (watchedSubscriptionId) {
      const subscription = subscriptions.find((sub: any) => sub.id === watchedSubscriptionId);
      if (subscription) {
        setValue('amount', subscription.amount);
        setValue('currency', subscription.currency);
      }
    }
  }, [watchedSubscriptionId, subscriptions, setValue]);

  // Create payment mutation
  const createMutation = useMutation({
    mutationFn: (data: PaymentFormData) => api.payments.create(data),
    onSuccess: () => {
      toast.success('Payment recorded successfully!');
      router.push('/payments');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to record payment');
    },
  });

  const onSubmit = (data: PaymentFormData) => {
    createMutation.mutate(data);
  };

  const getSelectedSubscription = () => {
    return subscriptions.find((sub: any) => sub.id === watchedSubscriptionId);
  };

  const getSelectedBusiness = () => {
    return businesses.find((biz: any) => biz.id === watchedBusinessId);
  };

  if (businessesLoading || subscriptionsLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading..." />
        </div>
      </Layout>
    );
  }

  // Show message if no businesses exist
  if (businesses.length === 0) {
    return (
      <Layout>
        <div className="max-w-2xl mx-auto">
          <div className="text-center py-12">
            <BuildingOfficeIcon className="h-12 w-12 text-text-tertiary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text-primary mb-2">No businesses found</h3>
            <p className="text-text-secondary mb-6">
              You need to create a business before you can record payments.
            </p>
            <button
              onClick={() => router.push('/businesses/new')}
              className="btn-primary"
            >
              Create Your First Business
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Record Payment - SubDash</title>
        <meta name="description" content="Record a new subscription payment" />
      </Head>

      <Layout>
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-text-primary">Record Payment</h1>
            <p className="text-sm text-text-secondary mt-1">
              Record a payment for one of your subscriptions
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Business and Subscription Selection */}
            <div className="card">
              <h3 className="text-lg font-medium text-text-primary mb-4 flex items-center">
                <BuildingOfficeIcon className="h-5 w-5 mr-2" />
                Business & Subscription
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Business */}
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Business *
                  </label>
                  <select
                    {...register('business_id')}
                    className={`input-primary w-full ${
                      errors.business_id ? 'border-error-500 focus:border-error-500' : ''
                    }`}
                  >
                    <option value="">Select a business</option>
                    {businesses.map((business: any) => (
                      <option key={business.id} value={business.id}>
                        {business.name}
                      </option>
                    ))}
                  </select>
                  {errors.business_id && (
                    <p className="text-error-600 text-sm mt-1">{errors.business_id.message}</p>
                  )}
                </div>

                {/* Subscription */}
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Subscription *
                  </label>
                  <select
                    {...register('subscription_id')}
                    disabled={!watchedBusinessId}
                    className={`input-primary w-full ${
                      errors.subscription_id ? 'border-error-500 focus:border-error-500' : ''
                    } ${!watchedBusinessId ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    <option value="">
                      {!watchedBusinessId ? 'Select a business first' : 'Select a subscription'}
                    </option>
                    {filteredSubscriptions.map((subscription: any) => (
                      <option key={subscription.id} value={subscription.id}>
                        {subscription.name} - {formatCurrency(subscription.amount, subscription.currency)}
                      </option>
                    ))}
                  </select>
                  {errors.subscription_id && (
                    <p className="text-error-600 text-sm mt-1">{errors.subscription_id.message}</p>
                  )}
                </div>
              </div>

              {/* Selected Subscription Info */}
              {watchedSubscriptionId && (
                <div className="mt-4 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-200 dark:border-primary-800">
                  <div className="flex items-start">
                    <InformationCircleIcon className="h-5 w-5 text-primary-600 mt-0.5 mr-2 flex-shrink-0" />
                    <div className="text-sm">
                      <p className="text-primary-800 dark:text-primary-200 font-medium">
                        Selected: {getSelectedSubscription()?.name}
                      </p>
                      <p className="text-primary-700 dark:text-primary-300 mt-1">
                        Business: {getSelectedBusiness()?.name} • 
                        Amount: {formatCurrency(getSelectedSubscription()?.amount || 0, getSelectedSubscription()?.currency || 'USD')}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Payment Details */}
            <div className="card">
              <h3 className="text-lg font-medium text-text-primary mb-4 flex items-center">
                <CurrencyDollarIcon className="h-5 w-5 mr-2" />
                Payment Details
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Amount */}
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Amount *
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    {...register('amount', { valueAsNumber: true })}
                    className={`input-primary w-full ${
                      errors.amount ? 'border-error-500 focus:border-error-500' : ''
                    }`}
                    placeholder="0.00"
                  />
                  {errors.amount && (
                    <p className="text-error-600 text-sm mt-1">{errors.amount.message}</p>
                  )}
                </div>

                {/* Currency */}
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Currency *
                  </label>
                  <select
                    {...register('currency')}
                    className={`input-primary w-full ${
                      errors.currency ? 'border-error-500 focus:border-error-500' : ''
                    }`}
                  >
                    {currencies.map((currency) => (
                      <option key={currency.code} value={currency.code}>
                        {currency.code} - {currency.name} ({currency.symbol})
                      </option>
                    ))}
                  </select>
                  {errors.currency && (
                    <p className="text-error-600 text-sm mt-1">{errors.currency.message}</p>
                  )}
                </div>

                {/* Payment Date */}
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Payment Date *
                  </label>
                  <input
                    type="date"
                    {...register('payment_date')}
                    className={`input-primary w-full ${
                      errors.payment_date ? 'border-error-500 focus:border-error-500' : ''
                    }`}
                  />
                  {errors.payment_date && (
                    <p className="text-error-600 text-sm mt-1">{errors.payment_date.message}</p>
                  )}
                </div>

                {/* Payment Method */}
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Payment Method *
                  </label>
                  <select
                    {...register('payment_method')}
                    className={`input-primary w-full ${
                      errors.payment_method ? 'border-error-500 focus:border-error-500' : ''
                    }`}
                  >
                    {paymentMethods.map((method) => (
                      <option key={method.value} value={method.value}>
                        {method.label}
                      </option>
                    ))}
                  </select>
                  {errors.payment_method && (
                    <p className="text-error-600 text-sm mt-1">{errors.payment_method.message}</p>
                  )}
                </div>

                {/* Status */}
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Status *
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {paymentStatuses.map((status) => (
                      <label key={status.value} className="relative">
                        <input
                          type="radio"
                          {...register('status')}
                          value={status.value}
                          className="sr-only peer"
                        />
                        <div className="p-3 border border-border-primary rounded-lg cursor-pointer transition-all peer-checked:border-primary-500 peer-checked:bg-primary-50 dark:peer-checked:bg-primary-900/20 hover:border-primary-300">
                          <div className="font-medium text-text-primary">{status.label}</div>
                          <div className="text-sm text-text-secondary mt-1">{status.description}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                  {errors.status && (
                    <p className="text-error-600 text-sm mt-1">{errors.status.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div className="card">
              <h3 className="text-lg font-medium text-text-primary mb-4 flex items-center">
                <DocumentTextIcon className="h-5 w-5 mr-2" />
                Additional Information
              </h3>
              
              <div className="space-y-4">
                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Description
                  </label>
                  <input
                    type="text"
                    {...register('description')}
                    className="input-primary w-full"
                    placeholder="Brief description of the payment"
                  />
                </div>

                {/* Transaction ID */}
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Transaction ID
                  </label>
                  <input
                    type="text"
                    {...register('transaction_id')}
                    className="input-primary w-full"
                    placeholder="Payment processor transaction ID"
                  />
                </div>

                {/* Notes */}
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Notes
                  </label>
                  <textarea
                    {...register('notes')}
                    rows={3}
                    className="input-primary w-full"
                    placeholder="Additional notes about this payment"
                  />
                </div>
              </div>
            </div>

            {/* Preview */}
            {watchedAmount && watchedCurrency && (
              <div className="card bg-background-secondary">
                <h3 className="text-lg font-medium text-text-primary mb-4">Payment Preview</h3>
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary-600 mb-2">
                    {formatCurrency(watchedAmount, watchedCurrency)}
                  </div>
                  <div className="text-text-secondary">
                    {getSelectedSubscription()?.name && (
                      <span>for {getSelectedSubscription()?.name}</span>
                    )}
                    {getSelectedBusiness()?.name && (
                      <span> • {getSelectedBusiness()?.name}</span>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex flex-col sm:flex-row gap-3 sm:justify-end">
              <button
                type="button"
                onClick={() => router.back()}
                className="btn-secondary"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="btn-primary flex items-center justify-center"
              >
                {isSubmitting ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Recording Payment...
                  </>
                ) : (
                  'Record Payment'
                )}
              </button>
            </div>
          </form>
        </div>
      </Layout>
    </>
  );
}