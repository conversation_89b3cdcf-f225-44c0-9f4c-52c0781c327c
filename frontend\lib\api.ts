import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { supabase } from './supabase';

// API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

// Create axios instance with default configuration
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  async (config: any) => {
    try {
      // Get current session from Supabase
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session?.access_token) {
        config.headers.Authorization = `Bearer ${session.access_token}`;
      }
    } catch (error) {
      console.warn('Failed to get auth token:', error);
    }
    
    return config;
  },
  (error: any) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: any) => {
    const originalRequest = error.config;
    
    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        // Try to refresh the session
        const { data: { session } } = await supabase.auth.refreshSession();
        
        if (session?.access_token) {
          originalRequest.headers.Authorization = `Bearer ${session.access_token}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // If refresh fails, redirect to login
        console.error('Token refresh failed:', refreshError);
        await supabase.auth.signOut();
        window.location.href = '/auth/signin';
      }
    }
    
    return Promise.reject(error);
  }
);

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Generic API methods
export const api = {
  // Generic HTTP methods
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await apiClient.get(url, config);
    return response.data;
  },

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await apiClient.post(url, data, config);
    return response.data;
  },

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await apiClient.put(url, data, config);
    return response.data;
  },

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await apiClient.patch(url, data, config);
    return response.data;
  },

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await apiClient.delete(url, config);
    return response.data;
  },

  // Business endpoints
  businesses: {
    async getAll(): Promise<ApiResponse<any[]>> {
      return api.get('/api/businesses');
    },

    async getById(id: string): Promise<ApiResponse<any>> {
      return api.get(`/api/businesses/${id}`);
    },

    async create(business: any): Promise<ApiResponse<any>> {
      return api.post('/api/businesses', business);
    },

    async update(id: string, business: any): Promise<ApiResponse<any>> {
      return api.put(`/api/businesses/${id}`, business);
    },

    async delete(id: string): Promise<ApiResponse<any>> {
      return api.delete(`/api/businesses/${id}`);
    },

    async getStats(id: string): Promise<ApiResponse<any>> {
      return api.get(`/api/businesses/${id}/stats`);
    },
  },

  // Subscription endpoints
  subscriptions: {
    async getAll(params?: any): Promise<ApiResponse<any[]>> {
      return api.get('/api/subscriptions', { params });
    },

    async getById(id: string): Promise<ApiResponse<any>> {
      return api.get(`/api/subscriptions/${id}`);
    },

    async create(subscription: any): Promise<ApiResponse<any>> {
      return api.post('/api/subscriptions', subscription);
    },

    async update(id: string, subscription: any): Promise<ApiResponse<any>> {
      return api.put(`/api/subscriptions/${id}`, subscription);
    },

    async delete(id: string): Promise<ApiResponse<any>> {
      return api.delete(`/api/subscriptions/${id}`);
    },

    async pause(id: string): Promise<ApiResponse<any>> {
      return api.patch(`/api/subscriptions/${id}/pause`);
    },

    async resume(id: string): Promise<ApiResponse<any>> {
      return api.patch(`/api/subscriptions/${id}/resume`);
    },

    async cancel(id: string): Promise<ApiResponse<any>> {
      return api.patch(`/api/subscriptions/${id}/cancel`);
    },
  },

  // Payment endpoints
  payments: {
    async getAll(params?: any): Promise<ApiResponse<any[]>> {
      return api.get('/api/payments', { params });
    },

    async getById(id: string): Promise<ApiResponse<any>> {
      return api.get(`/api/payments/${id}`);
    },

    async create(payment: any): Promise<ApiResponse<any>> {
      return api.post('/api/payments', payment);
    },

    async update(id: string, payment: any): Promise<ApiResponse<any>> {
      return api.put(`/api/payments/${id}`, payment);
    },

    async delete(id: string): Promise<ApiResponse<any>> {
      return api.delete(`/api/payments/${id}`);
    },
  },

  // Reimbursement endpoints
  reimbursements: {
    async getAll(businessId?: string): Promise<ApiResponse<any[]>> {
      const params = businessId ? { business_id: businessId } : {};
      return api.get('/api/reimbursements', { params });
    },

    async getById(id: string): Promise<ApiResponse<any>> {
      return api.get(`/api/reimbursements/${id}`);
    },

    async create(reimbursement: any): Promise<ApiResponse<any>> {
      return api.post('/api/reimbursements', reimbursement);
    },

    async update(id: string, reimbursement: any): Promise<ApiResponse<any>> {
      return api.put(`/api/reimbursements/${id}`, reimbursement);
    },

    async delete(id: string): Promise<ApiResponse<any>> {
      return api.delete(`/api/reimbursements/${id}`);
    },

    async approve(id: string, notes?: string): Promise<ApiResponse<any>> {
      return api.patch(`/api/reimbursements/${id}/approve`, { notes });
    },

    async reject(id: string, notes?: string): Promise<ApiResponse<any>> {
      return api.patch(`/api/reimbursements/${id}/reject`, { notes });
    },

    async markPaid(id: string): Promise<ApiResponse<any>> {
      return api.patch(`/api/reimbursements/${id}/paid`);
    },
  },

  // Currency endpoints
  currency: {
    async getRates(baseCurrency?: string): Promise<ApiResponse<any>> {
      const params = baseCurrency ? { base: baseCurrency } : {};
      return api.get('/api/currency/rates', { params });
    },

    async getHistoricalRates(fromCurrency: string, toCurrency: string, startDate: string, endDate: string): Promise<ApiResponse<any[]>> {
      return api.get('/api/currency/historical', {
        params: { from: fromCurrency, to: toCurrency, start_date: startDate, end_date: endDate }
      });
    },

    async convert(amount: number, fromCurrency: string, toCurrency: string, date?: string): Promise<ApiResponse<any>> {
      return api.post('/api/currency/convert', {
        amount,
        from_currency: fromCurrency,
        to_currency: toCurrency,
        date
      });
    },

    async getSupportedCurrencies(): Promise<ApiResponse<any[]>> {
      return api.get('/api/currency/supported');
    },
  },

  // Dashboard endpoints
  dashboard: {
    async getOverview(timeRange: string = '30d'): Promise<ApiResponse<any>> {
      return api.get('/api/dashboard/overview', { params: { time_range: timeRange } });
    },

    async getRecentActivity(limit: number = 10): Promise<ApiResponse<any[]>> {
      return api.get('/api/dashboard/activity', { params: { limit } });
    },

    async getUpcomingRenewals(limit: number = 10): Promise<ApiResponse<any[]>> {
      return api.get('/api/dashboard/renewals', { params: { limit } });
    },
  },

  // Reports endpoints
  reports: {
    async getSummary(businessId: string, params: {
      start_date: string;
      end_date: string;
      report_type?: string;
      group_by?: string;
      currency?: string;
    }): Promise<ApiResponse<any>> {
      return api.get(`/api/reports/summary`, {
        params: { business_id: businessId, ...params }
      });
    },

    async exportCSV(businessId: string, params: {
      export_type: string;
      start_date?: string;
      end_date?: string;
      include_fields?: string[];
    }): Promise<Blob> {
      const response = await apiClient.post('/api/reports/export/csv', {
        business_id: businessId,
        ...params
      }, {
        responseType: 'blob'
      });
      return response.data;
    },

    async getAnalytics(businessId: string, period: string = '30d'): Promise<ApiResponse<any>> {
      return api.get(`/api/reports/analytics/${businessId}`, {
        params: { period }
      });
    },

    async getReports(params?: any): Promise<ApiResponse<any[]>> {
      return api.get('/api/reports', { params });
    },

    async exportReport(reportType: string, params?: any): Promise<Blob> {
      const response = await apiClient.post('/api/reports/export', {
        report_type: reportType,
        ...params
      }, {
        responseType: 'blob'
      });
      return response.data;
    },
  },

  // User endpoints
  users: {
    async getProfile(): Promise<ApiResponse<any>> {
      return api.get('/api/users/profile');
    },

    async updateProfile(profile: any): Promise<ApiResponse<any>> {
      return api.put('/api/users/profile', profile);
    },

    async getStats(): Promise<ApiResponse<any>> {
      return api.get('/api/users/stats');
    },

    async updatePassword(currentPassword: string, newPassword: string): Promise<ApiResponse<any>> {
      return api.put('/api/users/password', {
        current_password: currentPassword,
        new_password: newPassword
      });
    },

    async updateNotifications(settings: any): Promise<ApiResponse<any>> {
      return api.put('/api/users/notifications', settings);
    },

    async deleteAccount(): Promise<ApiResponse<any>> {
      return api.delete('/api/users/account');
    },
  },

  // File upload endpoints
  files: {
    async upload(file: File, type: 'receipt' | 'document' = 'document'): Promise<ApiResponse<{ url: string; filename: string }>> {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);

      return api.post('/api/files/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    },

    async delete(filename: string): Promise<ApiResponse<any>> {
      return api.delete(`/api/files/${filename}`);
    },
  },
};

// Error handling utilities
export class ApiError extends Error {
  public status: number;
  public code?: string;
  public details?: any;

  constructor(message: string, status: number, code?: string, details?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}

export const handleApiError = (error: any): ApiError => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;
    return new ApiError(
      data?.message || data?.error || 'An error occurred',
      status,
      data?.code,
      data
    );
  } else if (error.request) {
    // Request was made but no response received
    return new ApiError('Network error - please check your connection', 0);
  } else {
    // Something else happened
    return new ApiError(error.message || 'An unexpected error occurred', 0);
  }
};

// Request/Response logging (only in development)
if (process.env.NODE_ENV === 'development') {
  apiClient.interceptors.request.use(
    (config: any) => {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data,
      });
      return config;
    },
    (error: any) => {
      console.error('❌ API Request Error:', error);
      return Promise.reject(error);
    }
  );

  apiClient.interceptors.response.use(
    (response: any) => {
      console.log(`✅ API Response: ${response.status} ${response.config.url}`, response.data);
      return response;
    },
    (error: any) => {
      console.error('❌ API Response Error:', {
        status: error.response?.status,
        url: error.config?.url,
        data: error.response?.data,
      });
      return Promise.reject(error);
    }
  );
}

export default api;